import { clientsClaim } from 'workbox-core';
import { precacheAndRoute, createHandlerBoundToURL } from 'workbox-precaching';
import { registerRoute, NavigationRoute } from 'workbox-routing';
import { StaleWhileRevalidate, CacheFirst, NetworkFirst } from 'workbox-strategies';
import { ExpirationPlugin } from 'workbox-expiration';
import { CacheableResponsePlugin } from 'workbox-cacheable-response';

clientsClaim();
self.skipWaiting();

// 预缓存和路由
precacheAndRoute(self.__WB_MANIFEST || []);

// 设置缓存名称前缀
const PREFIX = 'FET';
const VERSION = process.env.VUE_APP_VERSION || 'v1';
  
  // CSS files
  registerRoute(
    ({ request }) => request.destination === 'style' || request.url.endsWith('.css'),
    new StaleWhileRevalidate({
      cacheName: `${PREFIX}-css-${VERSION}`,
      plugins: [
        new CacheableResponsePlugin({
          statuses: [0, 200]
        }),
        new ExpirationPlugin({
          maxEntries: 100,
          maxAgeSeconds: 7 * 24 * 60 * 60 // 7 days
        })
      ]
    })
  );

  // JavaScript files
  registerRoute(
    ({ request }) => request.destination === 'script' || request.url.endsWith('.js'),
    new StaleWhileRevalidate({
      cacheName: `${PREFIX}-js-${VERSION}`,
      plugins: [
        new CacheableResponsePlugin({
          statuses: [0, 200]
        }),
        new ExpirationPlugin({
          maxEntries: 100,
          maxAgeSeconds: 7 * 24 * 60 * 60 // 7 days
        })
      ]
    })
  );
  
  // 新浪股票图表使用NetworkFirst但不缓存
  registerRoute(
    /^https:\/\/image\.sinajs\.cn\/newchart\//,
    new NetworkFirst({
      cacheName: "sina-charts",
      networkTimeoutSeconds: 10,
      plugins: [
        new ExpirationPlugin({
          maxEntries: 1, // 最多缓存1个，实际上会立即过期
          maxAgeSeconds: 1 // 1秒后过期，基本等于不缓存
        })
      ]
    })
  );

  // 缓存其他图片资源
  registerRoute(
    ({ url }) => {
      // 排除新浪股票图表
      if (url.hostname === 'image.sinajs.cn' && url.pathname.includes('/newchart/')) {
        return false;
      }
      // 匹配其他图片文件
      return /\.(?:png|gif|jpg|jpeg|svg)$/.test(url.pathname);
    },
    new StaleWhileRevalidate({
      cacheName: "images",
      plugins: [
        new ExpirationPlugin({
          maxEntries: 60,
          maxAgeSeconds: 30 * 24 * 60 * 60 // 设置缓存有效期为30天
        })
      ]
    })
  );
  
  // 我们很多资源在其他域名上，比如cdn、oss等，这里做单独处理，需要支持跨域
  registerRoute(
    /^https:\/\/image\.finevent\.top\/.*\.(jpe?g|png|gif|svg)/,
    new StaleWhileRevalidate({
      cacheName: "cdn-images",
      plugins: [
        new ExpirationPlugin({
          maxEntries: 60,
          maxAgeSeconds: 10 * 24 * 60 * 60 // 设置缓存有效期为10天
        })
      ],
      fetchOptions: {
        credentials: "include" // 支持跨域
      }
    })
  );
  
  // 我们很多资源在其他域名上，比如cdn、oss等，这里做单独处理，需要支持跨域
  registerRoute(
    /^https:\/\/assets\.finevent\.top\/.*\.(js|css)/,
    new StaleWhileRevalidate({
      cacheName: "oss-file",
      plugins: [
        new ExpirationPlugin({
          maxEntries: 60,
          maxAgeSeconds: 10 * 24 * 60 * 60 // 设置缓存有效期为10天
        })
      ],
      fetchOptions: {
        credentials: "include" // 支持跨域
      }
    })
  );

  // 股票网站禁用API缓存，确保数据实时性
  // registerRoute(
  //   /^https:\/\/.*\/api\//,
  //   new NetworkFirst({
  //     cacheName: "api-cache",
  //     networkTimeoutSeconds: 3,
  //     plugins: [
  //       new ExpirationPlugin({
  //         maxEntries: 50,
  //         maxAgeSeconds: 5 * 60 // 5分钟
  //       })
  //     ]
  //   })
  // );

  // 缓存字体文件
  registerRoute(
    /\.(?:woff|woff2|ttf|eot)$/,
    new CacheFirst({
      cacheName: "fonts-cache",
      plugins: [
        new ExpirationPlugin({
          maxEntries: 30,
          maxAgeSeconds: 365 * 24 * 60 * 60 // 1年
        })
      ]
    })
  );

  // 导航回退处理 - 确保SPA路由正常工作，但排除API请求
  try {
    const defaultHandler = createHandlerBoundToURL('/index.html');
    const navigationRoute = new NavigationRoute(defaultHandler, {
      denylist: [/^\/api\//]
    });
    registerRoute(navigationRoute);
  } catch (error) {
    console.error('Failed to setup navigation route:', error);
    // 如果创建导航路由失败，提供一个备用的处理方式
    self.addEventListener('fetch', (event) => {
      if (event.request.mode === 'navigate' && !event.request.url.includes('/api/')) {
        event.respondWith(
          fetch(event.request).catch(() => {
            return caches.match('/') || caches.match('/index.html') ||
                   new Response('App is offline', { status: 200, headers: { 'Content-Type': 'text/html' } });
          })
        );
      }
    });
  }

  // 预缓存关键页面（安全版本）
  self.addEventListener('install', (event) => {
    event.waitUntil(
      caches.open('offline-pages-v1').then((cache) => {
        // 只缓存确定存在的页面
        const criticalPages = ['/'];
        return Promise.allSettled(
          criticalPages.map(url =>
            fetch(url).then(response => {
              if (response.ok) {
                return cache.put(url, response);
              }
            }).catch(err => {
              console.warn(`Failed to cache ${url}:`, err);
            })
          )
        );
      })
    );
  });