const { defineConfig } = require('@vue/cli-service')
const path = require('path');
const CompressionPlugin = require("compression-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const resolve = dir => path.join(__dirname, dir);
const resourceUrl = process.env.VUE_APP_RESOURCE_URL
const isProduction = process.env.NODE_ENV === 'production'

module.exports = defineConfig({
  transpileDependencies: true,
  productionSourceMap: !isProduction,
  css: {
    extract: isProduction ? {
        ignoreOrder: true
    } : false,
    sourceMap: !isProduction, // 生产环境关闭 CSS source maps
    loaderOptions: {
        less: {
            additionalData: `@import "@/styles/variables.less";`,
            javascriptEnabled: true //less 配置
        }
    } // css预设器配置项
  },
  chainWebpack: config => {
    // 配置别名
    config.resolve.alias
        .set("@", resolve("./src"))
        .set("components", resolve("./src/components"))
        .set("constants", resolve("./src/constants"))
        .set("assets", resolve("./src/assets"))
        .set("utils", resolve("./src/utils"))
        .set("api", resolve("./src/api"))
        .set("styles", resolve("./src/styles"))
        .set("config", resolve("./src/config"))
    config.plugin('html').tap(args => {
        args[0].cdn = {
            css: [
                // `${resourceUrl}/x6.css`,
                // `${resourceUrl}/antd.min.css`,
                // `${resourceUrl}/vue-resize.css`

                'https://gcore.jsdelivr.net/npm/ant-design-vue@1.7.8/dist/antd.min.css',
                'https://gcore.jsdelivr.net/npm/vue-resize@1.0.1/dist/vue-resize.min.css',
                'https://gcore.jsdelivr.net/npm/@antv/x6@2.13.1/dist/index.css'
            ],
            js: [
            // vue must at first!
                // `${resourceUrl}/vue.min.js`,
                // `${resourceUrl}/vuex.min.js`,
                // `${resourceUrl}/vue-router.min.js`,
                // `${resourceUrl}/vue-router-tab.umd.min.js`,
                // `${resourceUrl}/moment.min.js`,
                // `${resourceUrl}/axios.min.js`,
                // `${resourceUrl}/antd-with-locales.min.js`,
                // `${resourceUrl}/vue-resize.min.js`,
                // `${resourceUrl}/vue-meta.min.js`,
                // `${resourceUrl}/lodash.min.js`,
                // `${resourceUrl}/echarts.min.js`,
                // `${resourceUrl}/marked.umd.min.js`,
                // `${resourceUrl}/clipboard.min.js`,
                // `${resourceUrl}/vue-lazyload.min.js`,
                // `${resourceUrl}/better-scroll.min.js`,
                // `${resourceUrl}/f2.min.js`,
                // `${resourceUrl}/Sortable.min.js`,
                // `${resourceUrl}/vuedraggable.umd.min.js`,
                // // `${resourceUrl}/html2canvas.min.js`,
                // `${resourceUrl}/turndown.cjs.min.js`,

                // cdn
                'https://gcore.jsdelivr.net/npm/vue@2.6.14',
                'https://gcore.jsdelivr.net/npm/vuex@3.6.2',
                'https://gcore.jsdelivr.net/npm/vue-router@3.6.5',
                'https://gcore.jsdelivr.net/npm/vue-router-tab@1.2.11/dist/lib/vue-router-tab.umd.min.js',
                'https://gcore.jsdelivr.net/npm/moment@2.30.1/moment.min.js',
                'https://gcore.jsdelivr.net/npm/axios@1.7.7/dist/axios.min.js',
                'https://gcore.jsdelivr.net/npm/ant-design-vue@1.7.8/dist/antd-with-locales.min.js',
                'https://gcore.jsdelivr.net/npm/vue-resize@1.0.1/dist/vue-resize.umd.min.js',
                'https://gcore.jsdelivr.net/npm/vue-meta@2.4.0/dist/vue-meta.min.js',
                'https://gcore.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js',
                'https://gcore.jsdelivr.net/npm/echarts@5.5.1/dist/echarts.min.js',
                'https://gcore.jsdelivr.net/npm/marked@7.0.1/lib/marked.umd.min.js',
                'https://gcore.jsdelivr.net/npm/clipboard@2.0.11/dist/clipboard.min.js',
                'https://gcore.jsdelivr.net/npm/vue-lazyload@1.3.3/vue-lazyload.min.js',
                'https://gcore.jsdelivr.net/npm/better-scroll@2.5.1/dist/better-scroll.min.js',
                'https://gcore.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js',
                'https://gcore.jsdelivr.net/npm/vuedraggable@2.24.3/dist/vuedraggable.umd.min.js',
                'https://gcore.jsdelivr.net/npm/turndown@7.2.0/lib/turndown.umd.js',
                'https://gcore.jsdelivr.net/npm/@antv/f2@3.8.13/dist/f2.min.js',
            ],
        };
        return args;
    });
    const oneOfsMap = config.module.rule("less").oneOfs.store;
    oneOfsMap.forEach(item => {
        item
        .use("style-resources-loader")
        .loader("style-resources-loader")
        .options({
        // 需要插入的文件路径
        patterns: "./src/styles/variables.less"
        // 需要插入的文件路径数组
        // patterns: ["./path/to/vars.less", "./path/to/mixins.less"]
        })
        .end();
    });

    config.module.rule('vue').use('vue-loader').loader('vue-loader')
        .tap(options => {
            options.prettify = false //关闭
            return options
        })
  },
  pwa: {
      // 一些基础配置
      name: 'FET',
      themeColor: '#ffffff',
      msTileColor: '#000000',
      appleMobileWebAppCapable: 'yes',
      appleMobileWebAppStatusBarStyle: 'black',

      /*
      * 两个模式，GenerateSW（默认）和 InjectManifest
      * GenerateSW: build项目时候，每次都会新建一个service worker文件
      * InjectManifest: 自定义的service worker文件，并且可以处理预缓存列表
      */
      workboxPluginMode: 'InjectManifest',
      workboxOptions: {
        // 自定义的service worker文件的位置
        swSrc: './src/service-worker.js',
        // 使用函数来精确控制哪些文件被排除
        exclude: [
          // 排除除了index.html之外的所有HTML文件
          (asset) => asset.name.endsWith('.html') && asset.name !== 'index.html'
        ],
        // ...other Workbox options...
        // importWorkboxFrom: "disabled", // 是否要引入线上的service-worker文件，我们只需要自己定义的文件，不需要谷歌提供的sw文件
      }
  },
  configureWebpack: {
      devServer: {
        historyApiFallback: true,
        allowedHosts: 'all',
        client: {
            overlay: false
        }
          // watchOptions: {
          //     ignored: /node_modules/,
          //     poll: 1000,
          // }
      },
      output: {
          libraryTarget: "umd"
      },
      optimization: {
          runtimeChunk: 'single',
          minimize: isProduction,
          usedExports: true,
          sideEffects: false,
          splitChunks: {
            chunks: 'all',
            maxInitialRequests: Infinity,
            minSize: 20000,
            maxSize: 244000,
            cacheGroups: {
              // 框架代码
              framework: {
                test: /[\\/]node_modules[\\/](vue|vue-router|vuex|axios)[\\/]/,
                name: 'framework',
                chunks: 'all',
                priority: 40,
                reuseExistingChunk: true
              },
              // UI库
              ui: {
                test: /[\\/]node_modules[\\/](ant-design-vue|@ant-design)[\\/]/,
                name: 'ui-library',
                chunks: 'all',
                priority: 30,
                reuseExistingChunk: true
              },
              // 工具库
              utils: {
                test: /[\\/]node_modules[\\/](lodash|moment|dayjs|echarts)[\\/]/,
                name: 'utils',
                chunks: 'all',
                priority: 20,
                reuseExistingChunk: true
              },
              // 其他第三方库
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name (module) {
                  const match = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)
                  if (!match) {
                    return 'vendor'
                  }
                  const packageName = match[1]
                  return `npm.${packageName.replace('@', '')}`
                },
                chunks: 'all',
                priority: 10,
                reuseExistingChunk: true
              },
              // 公共代码
              common: {
                name: 'chunk-common',
                minChunks: 2,
                priority: 5,
                chunks: 'all',
                reuseExistingChunk: true
              }
            }
          }
      },
      externals: {
          'vue': 'Vue',
          'vue-router': 'VueRouter',
          'vuex': 'Vuex',
          'axios': 'axios',
          'moment': 'moment',
          'lodash': '_',
          // 'ant-design-vue': 'Antd',
          'echarts': 'echarts',
          'marked': 'marked',
          'VueLazyload': 'VueLazyload',
          // 'mavon-editor': 'MavonEditor',
          // 'vue-router-tab': 'RouterTab',
          // 'vue-infinite-loading': 'InfiniteLoading',
          'vue-meta': 'VueMeta',
          // 'vue-moment': 'VueMoment',
          'vue-resize': 'VueResize',
          // 'vue-simple-context-menu': 'VueSimpleContextMenu',
          // 'vue-touch': 'VueTouch',
      },
      // resolve: {
      //   alias: {
      //     "@": resolve("src"),
      //   },
      //   fallback: {
      //     path: require.resolve("path-browserify"),
      //   },
      // },
      plugins: isProduction ? [
          new CompressionPlugin({
              test: /\.(js|css|html|json|svg)$/,// 匹配文件名
              threshold: 8192, // 对超过8k的数据压缩
              deleteOriginalAssets: false, // 不删除源文件
              minRatio: 0.8 // 压缩比
          }),
          new MiniCssExtractPlugin({
              ignoreOrder: true,
              filename: 'css/[name].[contenthash:8].css',
              chunkFilename: 'css/[name].[contenthash:8].css'
          })
      ] : [
          new MiniCssExtractPlugin({
              ignoreOrder: true,
          })
      ],
      // 性能优化配置
      performance: {
          hints: isProduction ? 'warning' : false,
          maxEntrypointSize: 512000, // 512KB
          maxAssetSize: 512000,
          assetFilter: function(assetFilename) {
              return !assetFilename.endsWith('.map')
          }
      },
      // 模块解析优化
      resolve: {
          extensions: ['.js', '.vue', '.json'],
          alias: {
              '@': resolve('./src'),
              'vue$': 'vue/dist/vue.runtime.esm.js'
          },
          modules: ['node_modules'],
          symlinks: false
      },
      // 缓存配置
      cache: isProduction ? false : {
          type: 'filesystem',
          buildDependencies: {
              config: [__filename]
          }
      }
      // module: {
      //     rules: [
      //       {
      //         test: /\.css$/i,
      //         use: [MiniCssExtractPlugin.loader, "css-loader"],
      //       },
      //     ],
      // },
  },
})
