# -*- coding: utf-8 -*-

"""
东方财富爬虫优化器
专门优化东方财富网站的请求策略，避免反爬机制
"""

import time
import random
import hashlib
import logging
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse, parse_qs, urlencode

logger = logging.getLogger(__name__)

class EastmoneyRequestOptimizer:
    """东方财富请求优化器"""
    
    def __init__(self):
        # 可用主机列表
        self.hosts = ['59', '60', '61', '62', '63']
        
        # 主机健康状态
        self.host_health = {host: {'score': 100, 'last_used': 0, 'failures': 0} 
                           for host in self.hosts}
        
        # 请求参数优化
        self.param_variations = {
            'ut': [
                'bd1d9ddb04089700cf9c27f6f7426281',
                'fa5fd1943c7b386f172d6893dbfba10b',
                '7eea3edcaed734bea9cbfc24409ed989'
            ],
            'wbp2u': [
                '|0|0|0|web',
                '|0|0|0|pc',
                '|0|0|0|h5'
            ]
        }
        
        # 请求频率控制
        self.request_history = []
        self.max_requests_per_minute = 30
        self.min_interval = 2.0
        
    def optimize_request_url(self, url: str, force_host_rotation: bool = False) -> str:
        """优化请求URL"""
        try:
            # 解析URL
            parsed = urlparse(url)
            
            # 主机轮换
            if 'push2.eastmoney.com' in url:
                url = self._rotate_host(url, force_host_rotation)
            
            # 参数优化
            url = self._optimize_parameters(url)
            
            # 添加时间戳和随机参数
            url = self._add_timestamp_and_random(url)
            
            return url
            
        except Exception as e:
            logger.error(f"Error optimizing URL {url}: {e}")
            return url
    
    def _rotate_host(self, url: str, force_rotation: bool = False) -> str:
        """轮换主机"""
        try:
            current_host = self._extract_current_host(url)
            
            # 选择最佳主机
            best_host = self._select_best_host(current_host, force_rotation)
            
            if best_host and best_host != current_host:
                url = url.replace(f'{current_host}.push2.eastmoney.com', 
                                f'{best_host}.push2.eastmoney.com')
                logger.debug(f"Rotated host from {current_host} to {best_host}")
            
            # 更新主机使用记录
            if best_host:
                self.host_health[best_host]['last_used'] = time.time()
            
            return url
            
        except Exception as e:
            logger.error(f"Error rotating host for {url}: {e}")
            return url
    
    def _extract_current_host(self, url: str) -> Optional[str]:
        """提取当前主机编号"""
        try:
            parsed = urlparse(url)
            hostname = parsed.hostname
            if hostname and '.push2.eastmoney.com' in hostname:
                return hostname.split('.')[0]
        except:
            pass
        return None
    
    def _select_best_host(self, current_host: Optional[str], force_rotation: bool) -> str:
        """选择最佳主机"""
        # 如果强制轮换，排除当前主机
        available_hosts = self.hosts.copy()
        if force_rotation and current_host in available_hosts:
            available_hosts.remove(current_host)
        
        if not available_hosts:
            available_hosts = self.hosts.copy()
        
        # 计算主机得分
        host_scores = []
        current_time = time.time()
        
        for host in available_hosts:
            health = self.host_health[host]
            
            # 基础得分
            score = health['score']
            
            # 时间惩罚（最近使用过的主机得分降低）
            time_since_use = current_time - health['last_used']
            if time_since_use < 60:  # 1分钟内使用过
                score -= 20
            elif time_since_use < 300:  # 5分钟内使用过
                score -= 10
            
            # 失败惩罚
            score -= health['failures'] * 15
            
            host_scores.append((host, score))
        
        # 选择得分最高的主机
        best_host = max(host_scores, key=lambda x: x[1])[0]
        return best_host
    
    def _optimize_parameters(self, url: str) -> str:
        """优化请求参数"""
        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)
            
            # 随机化某些参数
            for param, variations in self.param_variations.items():
                if param in params:
                    params[param] = [random.choice(variations)]
            
            # 重建URL
            new_query = urlencode(params, doseq=True)
            new_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}?{new_query}"
            
            return new_url
            
        except Exception as e:
            logger.error(f"Error optimizing parameters for {url}: {e}")
            return url
    
    def _add_timestamp_and_random(self, url: str) -> str:
        """添加时间戳和随机参数"""
        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)
            
            # 添加或更新时间戳
            current_timestamp = int(time.time() * 1000)
            params['_t'] = [str(current_timestamp)]
            
            # 添加或更新随机参数
            random_param = random.randint(1000, 9999)
            params['_r'] = [str(random_param)]
            
            # 重建URL
            new_query = urlencode(params, doseq=True)
            new_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}?{new_query}"
            
            return new_url
            
        except Exception as e:
            logger.error(f"Error adding timestamp to {url}: {e}")
            return url
    
    def calculate_request_delay(self) -> float:
        """计算请求延迟"""
        current_time = time.time()
        
        # 清理过期的请求历史
        self.request_history = [t for t in self.request_history 
                               if current_time - t < 60]
        
        # 检查频率限制
        if len(self.request_history) >= self.max_requests_per_minute:
            # 超过频率限制，计算需要等待的时间
            oldest_request = min(self.request_history)
            wait_time = 60 - (current_time - oldest_request)
            return max(wait_time, self.min_interval)
        
        # 检查最小间隔
        if self.request_history:
            last_request = max(self.request_history)
            time_since_last = current_time - last_request
            if time_since_last < self.min_interval:
                return self.min_interval - time_since_last
        
        # 添加随机延迟，模拟人类行为
        return random.uniform(0.5, 1.5)
    
    def record_request_success(self, url: str):
        """记录请求成功"""
        current_time = time.time()
        self.request_history.append(current_time)
        
        # 提升主机健康得分
        host = self._extract_current_host(url)
        if host and host in self.host_health:
            self.host_health[host]['score'] = min(100, 
                self.host_health[host]['score'] + 2)
            self.host_health[host]['failures'] = max(0, 
                self.host_health[host]['failures'] - 1)
    
    def record_request_failure(self, url: str, error_type: str = 'unknown'):
        """记录请求失败"""
        host = self._extract_current_host(url)
        if host and host in self.host_health:
            # 降低主机健康得分
            penalty = 10
            if 'connection' in error_type.lower():
                penalty = 20
            elif 'timeout' in error_type.lower():
                penalty = 15
            
            self.host_health[host]['score'] = max(0, 
                self.host_health[host]['score'] - penalty)
            self.host_health[host]['failures'] += 1
    
    def get_optimized_headers(self, url: str) -> Dict[str, str]:
        """获取优化的请求头"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': 'http://quote.eastmoney.com/',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # 根据URL类型调整Referer
        if 'api/qt/clist' in url:
            headers['Referer'] = 'http://quote.eastmoney.com/center/gridlist.html'
        elif 'api/qt/stock' in url:
            headers['Referer'] = 'http://quote.eastmoney.com/'
        
        return headers
    
    def should_use_proxy(self, url: str, failure_count: int = 0) -> bool:
        """判断是否应该使用代理"""
        # 如果连续失败次数过多，建议使用代理
        if failure_count >= 3:
            return True
        
        # 检查主机健康状况
        host = self._extract_current_host(url)
        if host and host in self.host_health:
            health = self.host_health[host]
            if health['score'] < 30 or health['failures'] >= 5:
                return True
        
        return False
    
    def get_host_statistics(self) -> Dict[str, Dict]:
        """获取主机统计信息"""
        return self.host_health.copy()
    
    def reset_host_health(self, host: Optional[str] = None):
        """重置主机健康状态"""
        if host:
            if host in self.host_health:
                self.host_health[host] = {'score': 100, 'last_used': 0, 'failures': 0}
        else:
            # 重置所有主机
            for h in self.host_health:
                self.host_health[h] = {'score': 100, 'last_used': 0, 'failures': 0}

# 全局优化器实例
_eastmoney_optimizer = None

def get_eastmoney_optimizer() -> EastmoneyRequestOptimizer:
    """获取东方财富优化器实例"""
    global _eastmoney_optimizer
    if _eastmoney_optimizer is None:
        _eastmoney_optimizer = EastmoneyRequestOptimizer()
    return _eastmoney_optimizer

def optimize_eastmoney_request(url: str, force_host_rotation: bool = False) -> Tuple[str, Dict[str, str], float]:
    """
    优化东方财富请求
    
    Returns:
        Tuple[str, Dict[str, str], float]: (优化后的URL, 请求头, 建议延迟)
    """
    optimizer = get_eastmoney_optimizer()
    
    # 优化URL
    optimized_url = optimizer.optimize_request_url(url, force_host_rotation)
    
    # 获取优化的请求头
    headers = optimizer.get_optimized_headers(optimized_url)
    
    # 计算延迟
    delay = optimizer.calculate_request_delay()
    
    return optimized_url, headers, delay

def record_eastmoney_result(url: str, success: bool, error_type: str = 'unknown'):
    """记录东方财富请求结果"""
    optimizer = get_eastmoney_optimizer()
    
    if success:
        optimizer.record_request_success(url)
    else:
        optimizer.record_request_failure(url, error_type)
