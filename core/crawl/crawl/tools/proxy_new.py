# -*- coding: utf-8 -*-

"""
代理工具模块 - 重构版本
提供代理获取、验证、管理等功能
支持多维度代理质量管理和智能选择
"""

import os
import time
import random
import logging
import threading
import requests
import json
import hashlib
import concurrent.futures
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from urllib.parse import urlparse
from enum import Enum
from abc import ABC, abstractmethod
from scrapy.utils.project import get_project_settings

logger = logging.getLogger(__name__)
settings = get_project_settings()

class ProxyStatus(Enum):
    """代理状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    FAILED = "failed"
    BANNED = "banned"

class ProxyType(Enum):
    """代理类型枚举"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"

@dataclass
class ProxyMetrics:
    """代理指标数据"""
    success_count: int = 0
    fail_count: int = 0
    total_requests: int = 0
    avg_response_time: float = 0.0
    last_success_time: float = 0.0
    last_fail_time: float = 0.0
    consecutive_failures: int = 0
    domain_success_rates: Dict[str, float] = field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.fail_count
        return self.success_count / total if total > 0 else 0.0
    
    @property
    def reliability_score(self) -> float:
        """可靠性评分"""
        if self.total_requests == 0:
            return 0.0
        
        # 基础成功率权重 (40%)
        base_score = self.success_rate * 0.4
        
        # 响应时间权重 (30%)
        time_score = max(0, 1 - self.avg_response_time / 10) * 0.3
        
        # 连续失败惩罚 (20%)
        failure_penalty = max(0, 1 - self.consecutive_failures / 5) * 0.2
        
        # 最近活跃度权重 (10%)
        current_time = time.time()
        time_since_last_success = current_time - self.last_success_time
        activity_score = max(0, 1 - time_since_last_success / 3600) * 0.1
        
        return base_score + time_score + failure_penalty + activity_score

@dataclass
class ProxyInfo:
    """代理信息类"""
    proxy: str
    proxy_type: ProxyType = ProxyType.HTTP
    status: ProxyStatus = ProxyStatus.INACTIVE
    created_time: float = field(default_factory=time.time)
    last_check_time: float = 0.0
    metrics: ProxyMetrics = field(default_factory=ProxyMetrics)
    tags: List[str] = field(default_factory=list)
    source: str = "unknown"
    
    @property
    def id(self) -> str:
        """代理唯一标识"""
        return hashlib.md5(self.proxy.encode()).hexdigest()
    
    @property
    def is_healthy(self) -> bool:
        """是否健康"""
        return (self.status == ProxyStatus.ACTIVE and 
                self.metrics.success_rate >= 0.5 and
                self.metrics.consecutive_failures < 3)
    
    def update_success(self, response_time: float, domain: str = None):
        """更新成功记录"""
        self.metrics.success_count += 1
        self.metrics.total_requests += 1
        self.metrics.last_success_time = time.time()
        self.metrics.consecutive_failures = 0
        
        # 更新平均响应时间
        if self.metrics.avg_response_time == 0:
            self.metrics.avg_response_time = response_time
        else:
            self.metrics.avg_response_time = (
                self.metrics.avg_response_time * 0.8 + response_time * 0.2
            )
        
        # 更新域名成功率
        if domain:
            if domain not in self.metrics.domain_success_rates:
                self.metrics.domain_success_rates[domain] = 1.0
            else:
                current_rate = self.metrics.domain_success_rates[domain]
                self.metrics.domain_success_rates[domain] = current_rate * 0.9 + 0.1
        
        self.status = ProxyStatus.ACTIVE
    
    def update_failure(self, domain: str = None):
        """更新失败记录"""
        self.metrics.fail_count += 1
        self.metrics.total_requests += 1
        self.metrics.last_fail_time = time.time()
        self.metrics.consecutive_failures += 1
        
        # 更新域名成功率
        if domain:
            if domain not in self.metrics.domain_success_rates:
                self.metrics.domain_success_rates[domain] = 0.0
            else:
                current_rate = self.metrics.domain_success_rates[domain]
                self.metrics.domain_success_rates[domain] = current_rate * 0.9
        
        # 根据连续失败次数更新状态
        if self.metrics.consecutive_failures >= 5:
            self.status = ProxyStatus.FAILED
        elif self.metrics.consecutive_failures >= 3:
            self.status = ProxyStatus.INACTIVE

class ProxyProvider(ABC):
    """代理提供者抽象基类"""
    
    @abstractmethod
    def get_proxies(self, count: int = 1) -> List[str]:
        """获取代理列表"""
        pass
    
    @abstractmethod
    def validate_proxy(self, proxy: str) -> bool:
        """验证代理可用性"""
        pass

class APIProxyProvider(ProxyProvider):
    """API代理提供者"""
    
    def __init__(self, api_url: str, timeout: int = 10):
        self.api_url = api_url
        self.timeout = timeout
    
    def get_proxies(self, count: int = 1) -> List[str]:
        """从API获取代理"""
        proxies = []
        for _ in range(count):
            try:
                response = requests.get(f"{self.api_url}/get/", timeout=self.timeout)
                if response.status_code == 200:
                    data = response.json()
                    proxy = data.get('proxy')
                    if proxy:
                        proxies.append(proxy)
            except Exception as e:
                logger.error(f"Failed to get proxy from API: {e}")
        return proxies
    
    def validate_proxy(self, proxy: str) -> bool:
        """验证代理可用性"""
        try:
            proxies = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}"
            }
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxies,
                timeout=5,
                verify=False
            )
            return response.status_code == 200
        except Exception:
            return False

class ProxySelector(ABC):
    """代理选择器抽象基类"""
    
    @abstractmethod
    def select_proxy(self, proxies: List[ProxyInfo], target_url: str = None) -> Optional[ProxyInfo]:
        """选择最佳代理"""
        pass

class SmartProxySelector(ProxySelector):
    """智能代理选择器"""
    
    def select_proxy(self, proxies: List[ProxyInfo], target_url: str = None) -> Optional[ProxyInfo]:
        """智能选择代理"""
        if not proxies:
            return None
        
        # 过滤健康的代理
        healthy_proxies = [p for p in proxies if p.is_healthy]
        if not healthy_proxies:
            # 如果没有健康的代理，选择状态最好的
            healthy_proxies = sorted(proxies, key=lambda x: x.metrics.reliability_score, reverse=True)[:3]
        
        if not healthy_proxies:
            return None
        
        # 如果有目标URL，考虑域名特定的成功率
        if target_url:
            domain = urlparse(target_url).netloc
            domain_aware_proxies = []
            
            for proxy in healthy_proxies:
                domain_rate = proxy.metrics.domain_success_rates.get(domain, 0.5)
                # 综合评分：可靠性评分 * 域名成功率
                combined_score = proxy.metrics.reliability_score * (0.7 + domain_rate * 0.3)
                domain_aware_proxies.append((proxy, combined_score))
            
            # 按综合评分排序
            domain_aware_proxies.sort(key=lambda x: x[1], reverse=True)
            
            # 在前3个中随机选择，避免过度使用同一代理
            top_proxies = [p[0] for p in domain_aware_proxies[:3]]
            return random.choice(top_proxies)
        
        # 没有目标URL时，按可靠性评分选择
        healthy_proxies.sort(key=lambda x: x.metrics.reliability_score, reverse=True)
        return random.choice(healthy_proxies[:3])

class RoundRobinProxySelector(ProxySelector):
    """轮询代理选择器"""
    
    def __init__(self):
        self.current_index = 0
        self.lock = threading.Lock()
    
    def select_proxy(self, proxies: List[ProxyInfo], target_url: str = None) -> Optional[ProxyInfo]:
        """轮询选择代理"""
        if not proxies:
            return None
        
        healthy_proxies = [p for p in proxies if p.is_healthy]
        if not healthy_proxies:
            healthy_proxies = proxies
        
        with self.lock:
            proxy = healthy_proxies[self.current_index % len(healthy_proxies)]
            self.current_index += 1
            return proxy

class RandomProxySelector(ProxySelector):
    """随机代理选择器"""

    def select_proxy(self, proxies: List[ProxyInfo], target_url: str = None) -> Optional[ProxyInfo]:
        """随机选择代理"""
        if not proxies:
            return None

        healthy_proxies = [p for p in proxies if p.is_healthy]
        if not healthy_proxies:
            healthy_proxies = proxies

        return random.choice(healthy_proxies)

class ProxyPoolManager:
    """代理池管理器 - 重构版本"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._load_default_config()
        self.proxies: Dict[str, ProxyInfo] = {}
        self.providers: List[ProxyProvider] = []
        self.selector: ProxySelector = SmartProxySelector()
        self.lock = threading.Lock()

        # 配置参数
        self.max_pool_size = self.config.get('max_pool_size', 100)
        self.min_pool_size = self.config.get('min_pool_size', 10)
        self.check_interval = self.config.get('check_interval', 300)
        self.validation_timeout = self.config.get('validation_timeout', 5)
        self.max_consecutive_failures = self.config.get('max_consecutive_failures', 3)

        # 初始化提供者
        self._init_providers()

        # 启动后台任务
        self._start_background_tasks()

    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'max_pool_size': 100,
            'min_pool_size': 10,
            'check_interval': 300,
            'validation_timeout': 5,
            'max_consecutive_failures': 3,
            'api_urls': [
                os.getenv('BB_PROXY_URL', 'http://***********:5010'),
                os.getenv('BB_PROXY_URL2', '')
            ],
            'selector_type': 'smart'  # smart, round_robin, random
        }

    def _init_providers(self):
        """初始化代理提供者"""
        api_urls = self.config.get('api_urls', [])
        for url in api_urls:
            if url:
                provider = APIProxyProvider(url, self.validation_timeout)
                self.providers.append(provider)
                logger.info(f"Added API proxy provider: {url}")

    def _start_background_tasks(self):
        """启动后台任务"""
        # 健康检查线程
        health_check_thread = threading.Thread(
            target=self._health_check_worker,
            daemon=True
        )
        health_check_thread.start()

        # 代理补充线程
        refill_thread = threading.Thread(
            target=self._refill_worker,
            daemon=True
        )
        refill_thread.start()

        logger.info("Background tasks started")

    def _health_check_worker(self):
        """健康检查工作线程"""
        while True:
            try:
                self._perform_health_check()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Health check error: {e}")
                time.sleep(60)

    def _refill_worker(self):
        """代理补充工作线程"""
        while True:
            try:
                if len(self.proxies) < self.min_pool_size:
                    self._refill_proxies()
                time.sleep(30)  # 每30秒检查一次
            except Exception as e:
                logger.error(f"Refill worker error: {e}")
                time.sleep(60)

    def _perform_health_check(self):
        """执行健康检查"""
        with self.lock:
            proxies_to_check = list(self.proxies.values())

        logger.info(f"Performing health check on {len(proxies_to_check)} proxies")

        # 并发检查代理
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_proxy = {
                executor.submit(self._check_proxy_health, proxy): proxy
                for proxy in proxies_to_check
            }

            for future in concurrent.futures.as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                try:
                    is_healthy = future.result()
                    if not is_healthy:
                        self._handle_unhealthy_proxy(proxy)
                except Exception as e:
                    logger.error(f"Health check failed for {proxy.proxy}: {e}")
                    self._handle_unhealthy_proxy(proxy)

    def _check_proxy_health(self, proxy_info: ProxyInfo) -> bool:
        """检查单个代理健康状态"""
        try:
            proxies = {
                "http": f"http://{proxy_info.proxy}",
                "https": f"http://{proxy_info.proxy}"
            }

            start_time = time.time()
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxies,
                timeout=self.validation_timeout,
                verify=False
            )
            response_time = time.time() - start_time

            if response.status_code == 200:
                proxy_info.update_success(response_time)
                proxy_info.last_check_time = time.time()
                return True
            else:
                proxy_info.update_failure()
                return False

        except Exception as e:
            proxy_info.update_failure()
            return False

    def _handle_unhealthy_proxy(self, proxy_info: ProxyInfo):
        """处理不健康的代理"""
        if proxy_info.metrics.consecutive_failures >= self.max_consecutive_failures:
            with self.lock:
                if proxy_info.id in self.proxies:
                    del self.proxies[proxy_info.id]
                    logger.info(f"Removed unhealthy proxy: {proxy_info.proxy}")

    def _refill_proxies(self):
        """补充代理"""
        needed = self.min_pool_size - len(self.proxies)
        if needed <= 0:
            return

        logger.info(f"Refilling {needed} proxies")

        for provider in self.providers:
            try:
                new_proxies = provider.get_proxies(needed)
                for proxy_str in new_proxies:
                    if proxy_str and proxy_str not in [p.proxy for p in self.proxies.values()]:
                        proxy_info = ProxyInfo(
                            proxy=proxy_str,
                            source=provider.__class__.__name__
                        )

                        with self.lock:
                            self.proxies[proxy_info.id] = proxy_info

                        logger.debug(f"Added new proxy: {proxy_str}")
                        needed -= 1

                        if needed <= 0:
                            break

            except Exception as e:
                logger.error(f"Failed to get proxies from provider: {e}")

        logger.info(f"Proxy pool size after refill: {len(self.proxies)}")

    def get_proxy(self, target_url: str = None) -> Optional[str]:
        """获取代理"""
        with self.lock:
            proxy_list = list(self.proxies.values())

        if not proxy_list:
            # 尝试立即获取一些代理
            self._refill_proxies()
            with self.lock:
                proxy_list = list(self.proxies.values())

        if not proxy_list:
            logger.warning("No proxies available")
            return None

        selected_proxy = self.selector.select_proxy(proxy_list, target_url)
        if selected_proxy:
            return selected_proxy.proxy

        return None

    def report_success(self, proxy: str, response_time: float, target_url: str = None):
        """报告代理使用成功"""
        proxy_id = hashlib.md5(proxy.encode()).hexdigest()

        with self.lock:
            if proxy_id in self.proxies:
                domain = urlparse(target_url).netloc if target_url else None
                self.proxies[proxy_id].update_success(response_time, domain)
                logger.debug(f"Reported success for proxy: {proxy}")

    def report_failure(self, proxy: str, target_url: str = None):
        """报告代理使用失败"""
        proxy_id = hashlib.md5(proxy.encode()).hexdigest()

        with self.lock:
            if proxy_id in self.proxies:
                domain = urlparse(target_url).netloc if target_url else None
                self.proxies[proxy_id].update_failure(domain)
                logger.debug(f"Reported failure for proxy: {proxy}")

    def get_stats(self) -> Dict[str, Any]:
        """获取代理池统计信息"""
        with self.lock:
            proxy_list = list(self.proxies.values())

        if not proxy_list:
            return {
                'total_proxies': 0,
                'active_proxies': 0,
                'average_success_rate': 0.0,
                'average_response_time': 0.0
            }

        active_proxies = [p for p in proxy_list if p.status == ProxyStatus.ACTIVE]
        success_rates = [p.metrics.success_rate for p in proxy_list if p.metrics.total_requests > 0]
        response_times = [p.metrics.avg_response_time for p in proxy_list if p.metrics.avg_response_time > 0]

        return {
            'total_proxies': len(proxy_list),
            'active_proxies': len(active_proxies),
            'average_success_rate': sum(success_rates) / len(success_rates) if success_rates else 0.0,
            'average_response_time': sum(response_times) / len(response_times) if response_times else 0.0,
            'proxy_details': [
                {
                    'proxy': p.proxy,
                    'status': p.status.value,
                    'success_rate': p.metrics.success_rate,
                    'response_time': p.metrics.avg_response_time,
                    'total_requests': p.metrics.total_requests,
                    'reliability_score': p.metrics.reliability_score
                }
                for p in proxy_list
            ]
        }

    def set_selector(self, selector_type: str):
        """设置代理选择器"""
        if selector_type == 'smart':
            self.selector = SmartProxySelector()
        elif selector_type == 'round_robin':
            self.selector = RoundRobinProxySelector()
        elif selector_type == 'random':
            self.selector = RandomProxySelector()
        else:
            raise ValueError(f"Unknown selector type: {selector_type}")

        logger.info(f"Proxy selector set to: {selector_type}")

    def add_provider(self, provider: ProxyProvider):
        """添加代理提供者"""
        self.providers.append(provider)
        logger.info(f"Added proxy provider: {provider.__class__.__name__}")

    def remove_proxy(self, proxy: str):
        """移除指定代理"""
        proxy_id = hashlib.md5(proxy.encode()).hexdigest()

        with self.lock:
            if proxy_id in self.proxies:
                del self.proxies[proxy_id]
                logger.info(f"Removed proxy: {proxy}")

    def clear_failed_proxies(self):
        """清理失败的代理"""
        with self.lock:
            failed_proxies = [
                proxy_id for proxy_id, proxy_info in self.proxies.items()
                if proxy_info.status == ProxyStatus.FAILED
            ]

            for proxy_id in failed_proxies:
                del self.proxies[proxy_id]

        logger.info(f"Cleared {len(failed_proxies)} failed proxies")

    def export_config(self) -> Dict[str, Any]:
        """导出配置"""
        return {
            'config': self.config,
            'stats': self.get_stats(),
            'providers': [p.__class__.__name__ for p in self.providers],
            'selector': self.selector.__class__.__name__
        }

# 全局代理池实例
_proxy_pool_manager = None
_lock = threading.Lock()

def get_proxy_pool_manager() -> ProxyPoolManager:
    """获取代理池管理器实例（单例模式）"""
    global _proxy_pool_manager
    if _proxy_pool_manager is None:
        with _lock:
            if _proxy_pool_manager is None:
                _proxy_pool_manager = ProxyPoolManager()
    return _proxy_pool_manager

# 兼容性函数
def get_proxy(target_url: str = None) -> Dict[str, Optional[str]]:
    """获取代理（兼容旧接口）"""
    logger.info('Getting proxy...')
    manager = get_proxy_pool_manager()
    proxy = manager.get_proxy(target_url)
    return {'proxy': proxy}

def delete_proxy(proxy: str, error_type: str = "", target_url: str = None):
    """删除代理（兼容旧接口）"""
    logger.info('Reporting proxy failure...')
    manager = get_proxy_pool_manager()
    manager.report_failure(proxy, target_url)

def report_proxy_success(proxy: str, response_time: float, target_url: str = None):
    """报告代理成功（新接口）"""
    manager = get_proxy_pool_manager()
    manager.report_success(proxy, response_time, target_url)

def report_proxy_failure(proxy: str, target_url: str = None):
    """报告代理失败（新接口）"""
    manager = get_proxy_pool_manager()
    manager.report_failure(proxy, target_url)

def get_proxy_stats() -> Dict[str, Any]:
    """获取代理统计信息"""
    manager = get_proxy_pool_manager()
    return manager.get_stats()

def get_random_proxy() -> Optional[str]:
    """获取随机代理（兼容旧接口）"""
    result = get_proxy()
    return result.get('proxy')

def validUsefulProxy(proxy: str) -> bool:
    """验证代理可用性（兼容旧接口）"""
    if isinstance(proxy, bytes):
        proxy = proxy.decode("utf8")

    try:
        proxies = {
            "http": f"http://{proxy}",
            "https": f"http://{proxy}"
        }
        response = requests.get(
            'http://httpbin.org/ip',
            proxies=proxies,
            timeout=5,
            verify=False
        )
        return response.status_code == 200
    except Exception:
        return False
