# -*- coding: utf-8 -*-

"""
代理质量管理系统
提供代理质量评估、分级、监控和优化功能
"""

import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)

class ProxyGrade(Enum):
    """代理等级"""
    PREMIUM = "premium"      # 优质代理
    GOOD = "good"           # 良好代理
    AVERAGE = "average"     # 普通代理
    POOR = "poor"          # 较差代理
    BLACKLIST = "blacklist" # 黑名单代理

class QualityMetric(Enum):
    """质量指标"""
    SUCCESS_RATE = "success_rate"
    RESPONSE_TIME = "response_time"
    STABILITY = "stability"
    DOMAIN_COMPATIBILITY = "domain_compatibility"
    GEOGRAPHIC_COVERAGE = "geographic_coverage"

@dataclass
class QualityScore:
    """质量评分"""
    success_rate_score: float = 0.0      # 成功率评分 (0-100)
    response_time_score: float = 0.0     # 响应时间评分 (0-100)
    stability_score: float = 0.0         # 稳定性评分 (0-100)
    domain_score: float = 0.0            # 域名兼容性评分 (0-100)
    geographic_score: float = 0.0        # 地理覆盖评分 (0-100)
    
    @property
    def overall_score(self) -> float:
        """综合评分"""
        weights = {
            'success_rate': 0.35,
            'response_time': 0.25,
            'stability': 0.20,
            'domain': 0.15,
            'geographic': 0.05
        }
        
        return (
            self.success_rate_score * weights['success_rate'] +
            self.response_time_score * weights['response_time'] +
            self.stability_score * weights['stability'] +
            self.domain_score * weights['domain'] +
            self.geographic_score * weights['geographic']
        )
    
    @property
    def grade(self) -> ProxyGrade:
        """根据综合评分确定代理等级"""
        score = self.overall_score
        if score >= 90:
            return ProxyGrade.PREMIUM
        elif score >= 75:
            return ProxyGrade.GOOD
        elif score >= 60:
            return ProxyGrade.AVERAGE
        elif score >= 40:
            return ProxyGrade.POOR
        else:
            return ProxyGrade.BLACKLIST

@dataclass
class ProxyQualityData:
    """代理质量数据"""
    proxy_id: str
    proxy_address: str
    
    # 基础统计
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    # 响应时间统计
    response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    avg_response_time: float = 0.0
    
    # 稳定性统计
    consecutive_successes: int = 0
    consecutive_failures: int = 0
    max_consecutive_failures: int = 0
    uptime_percentage: float = 100.0
    
    # 域名兼容性
    domain_stats: Dict[str, Dict[str, int]] = field(default_factory=dict)
    
    # 地理位置信息
    country: str = "unknown"
    region: str = "unknown"
    isp: str = "unknown"
    
    # 时间戳
    first_seen: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)
    last_success: float = 0.0
    last_failure: float = 0.0
    
    # 质量评分
    quality_score: QualityScore = field(default_factory=QualityScore)
    
    def update_success(self, response_time: float, domain: str = None):
        """更新成功记录"""
        self.total_requests += 1
        self.successful_requests += 1
        self.consecutive_successes += 1
        self.consecutive_failures = 0
        self.last_used = time.time()
        self.last_success = time.time()
        
        # 更新响应时间
        self.response_times.append(response_time)
        if self.response_times:
            self.avg_response_time = statistics.mean(self.response_times)
        
        # 更新域名统计
        if domain:
            if domain not in self.domain_stats:
                self.domain_stats[domain] = {'success': 0, 'failure': 0}
            self.domain_stats[domain]['success'] += 1
        
        # 重新计算质量评分
        self._recalculate_quality_score()
    
    def update_failure(self, domain: str = None):
        """更新失败记录"""
        self.total_requests += 1
        self.failed_requests += 1
        self.consecutive_failures += 1
        self.consecutive_successes = 0
        self.max_consecutive_failures = max(
            self.max_consecutive_failures, 
            self.consecutive_failures
        )
        self.last_used = time.time()
        self.last_failure = time.time()
        
        # 更新域名统计
        if domain:
            if domain not in self.domain_stats:
                self.domain_stats[domain] = {'success': 0, 'failure': 0}
            self.domain_stats[domain]['failure'] += 1
        
        # 重新计算质量评分
        self._recalculate_quality_score()
    
    def _recalculate_quality_score(self):
        """重新计算质量评分"""
        # 成功率评分
        if self.total_requests > 0:
            success_rate = self.successful_requests / self.total_requests
            self.quality_score.success_rate_score = success_rate * 100
        
        # 响应时间评分 (响应时间越短评分越高)
        if self.avg_response_time > 0:
            # 假设5秒以下为满分，10秒以上为0分
            time_score = max(0, min(100, (10 - self.avg_response_time) / 10 * 100))
            self.quality_score.response_time_score = time_score
        
        # 稳定性评分 (基于连续失败次数和正常运行时间)
        stability_penalty = min(50, self.max_consecutive_failures * 10)
        self.quality_score.stability_score = max(0, 100 - stability_penalty)
        
        # 域名兼容性评分
        if self.domain_stats:
            domain_scores = []
            for domain, stats in self.domain_stats.items():
                total = stats['success'] + stats['failure']
                if total > 0:
                    domain_success_rate = stats['success'] / total
                    domain_scores.append(domain_success_rate * 100)
            
            if domain_scores:
                self.quality_score.domain_score = statistics.mean(domain_scores)
        
        # 地理覆盖评分 (简化版本，基于已知信息)
        geo_score = 50  # 默认分数
        if self.country != "unknown":
            geo_score += 25
        if self.region != "unknown":
            geo_score += 15
        if self.isp != "unknown":
            geo_score += 10
        self.quality_score.geographic_score = min(100, geo_score)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def is_healthy(self) -> bool:
        """是否健康"""
        return (
            self.consecutive_failures < 5 and
            self.success_rate >= 0.5 and
            self.quality_score.overall_score >= 40
        )

class ProxyQualityManager:
    """代理质量管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._load_default_config()
        self.quality_data: Dict[str, ProxyQualityData] = {}
        self.lock = threading.Lock()
        
        # 配置参数
        self.min_requests_for_grading = self.config.get('min_requests_for_grading', 10)
        self.quality_check_interval = self.config.get('quality_check_interval', 300)
        self.blacklist_threshold = self.config.get('blacklist_threshold', 20)
        self.cleanup_interval = self.config.get('cleanup_interval', 3600)
        
        # 启动后台任务
        self._start_background_tasks()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'min_requests_for_grading': 10,
            'quality_check_interval': 300,
            'blacklist_threshold': 20,
            'cleanup_interval': 3600,
            'grade_weights': {
                'success_rate': 0.35,
                'response_time': 0.25,
                'stability': 0.20,
                'domain_compatibility': 0.15,
                'geographic_coverage': 0.05
            }
        }
    
    def _start_background_tasks(self):
        """启动后台任务"""
        # 质量检查线程
        quality_check_thread = threading.Thread(
            target=self._quality_check_worker,
            daemon=True
        )
        quality_check_thread.start()
        
        # 数据清理线程
        cleanup_thread = threading.Thread(
            target=self._cleanup_worker,
            daemon=True
        )
        cleanup_thread.start()
        
        logger.info("Proxy quality manager background tasks started")
    
    def _quality_check_worker(self):
        """质量检查工作线程"""
        while True:
            try:
                self._perform_quality_assessment()
                time.sleep(self.quality_check_interval)
            except Exception as e:
                logger.error(f"Quality check error: {e}")
                time.sleep(60)
    
    def _cleanup_worker(self):
        """数据清理工作线程"""
        while True:
            try:
                self._cleanup_old_data()
                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"Cleanup worker error: {e}")
                time.sleep(300)
    
    def _perform_quality_assessment(self):
        """执行质量评估"""
        with self.lock:
            quality_data_list = list(self.quality_data.values())
        
        logger.info(f"Performing quality assessment on {len(quality_data_list)} proxies")
        
        for data in quality_data_list:
            if data.total_requests >= self.min_requests_for_grading:
                data._recalculate_quality_score()
                
                # 检查是否需要加入黑名单
                if (data.consecutive_failures >= self.blacklist_threshold or
                    data.quality_score.overall_score < 20):
                    logger.warning(f"Proxy {data.proxy_address} added to blacklist")
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        current_time = time.time()
        cutoff_time = current_time - (7 * 24 * 3600)  # 7天前
        
        with self.lock:
            old_proxies = [
                proxy_id for proxy_id, data in self.quality_data.items()
                if data.last_used < cutoff_time
            ]
            
            for proxy_id in old_proxies:
                del self.quality_data[proxy_id]
        
        if old_proxies:
            logger.info(f"Cleaned up {len(old_proxies)} old proxy records")
    
    def record_proxy_usage(self, proxy_id: str, proxy_address: str, 
                          success: bool, response_time: float = 0.0, 
                          domain: str = None):
        """记录代理使用情况"""
        with self.lock:
            if proxy_id not in self.quality_data:
                self.quality_data[proxy_id] = ProxyQualityData(
                    proxy_id=proxy_id,
                    proxy_address=proxy_address
                )
            
            data = self.quality_data[proxy_id]
            
            if success:
                data.update_success(response_time, domain)
            else:
                data.update_failure(domain)

    def get_proxy_grade(self, proxy_id: str) -> Optional[ProxyGrade]:
        """获取代理等级"""
        with self.lock:
            if proxy_id in self.quality_data:
                return self.quality_data[proxy_id].quality_score.grade
        return None

    def get_quality_score(self, proxy_id: str) -> Optional[QualityScore]:
        """获取质量评分"""
        with self.lock:
            if proxy_id in self.quality_data:
                return self.quality_data[proxy_id].quality_score
        return None

    def get_proxies_by_grade(self, grade: ProxyGrade) -> List[str]:
        """根据等级获取代理列表"""
        with self.lock:
            return [
                data.proxy_address for data in self.quality_data.values()
                if data.quality_score.grade == grade and data.is_healthy
            ]

    def get_best_proxies(self, count: int = 10, domain: str = None) -> List[Tuple[str, float]]:
        """获取最佳代理列表"""
        with self.lock:
            candidates = []

            for data in self.quality_data.values():
                if not data.is_healthy:
                    continue

                score = data.quality_score.overall_score

                # 如果指定了域名，考虑域名特定的成功率
                if domain and domain in data.domain_stats:
                    domain_stats = data.domain_stats[domain]
                    total = domain_stats['success'] + domain_stats['failure']
                    if total > 0:
                        domain_success_rate = domain_stats['success'] / total
                        # 域名成功率权重为30%
                        score = score * 0.7 + domain_success_rate * 100 * 0.3

                candidates.append((data.proxy_address, score))

            # 按评分排序并返回前N个
            candidates.sort(key=lambda x: x[1], reverse=True)
            return candidates[:count]

    def get_quality_report(self) -> Dict[str, Any]:
        """获取质量报告"""
        with self.lock:
            total_proxies = len(self.quality_data)
            if total_proxies == 0:
                return {
                    'total_proxies': 0,
                    'grade_distribution': {},
                    'average_scores': {},
                    'health_status': {}
                }

            # 等级分布
            grade_distribution = defaultdict(int)
            scores = defaultdict(list)
            health_count = {'healthy': 0, 'unhealthy': 0}

            for data in self.quality_data.values():
                grade_distribution[data.quality_score.grade.value] += 1

                scores['overall'].append(data.quality_score.overall_score)
                scores['success_rate'].append(data.quality_score.success_rate_score)
                scores['response_time'].append(data.quality_score.response_time_score)
                scores['stability'].append(data.quality_score.stability_score)
                scores['domain'].append(data.quality_score.domain_score)
                scores['geographic'].append(data.quality_score.geographic_score)

                if data.is_healthy:
                    health_count['healthy'] += 1
                else:
                    health_count['unhealthy'] += 1

            # 计算平均分
            average_scores = {}
            for metric, score_list in scores.items():
                if score_list:
                    average_scores[metric] = {
                        'mean': statistics.mean(score_list),
                        'median': statistics.median(score_list),
                        'std': statistics.stdev(score_list) if len(score_list) > 1 else 0
                    }

            return {
                'total_proxies': total_proxies,
                'grade_distribution': dict(grade_distribution),
                'average_scores': average_scores,
                'health_status': health_count,
                'quality_trends': self._get_quality_trends()
            }

    def _get_quality_trends(self) -> Dict[str, Any]:
        """获取质量趋势"""
        # 简化版本的趋势分析
        current_time = time.time()
        recent_cutoff = current_time - 3600  # 最近1小时

        recent_data = []
        older_data = []

        with self.lock:
            for data in self.quality_data.values():
                if data.last_used >= recent_cutoff:
                    recent_data.append(data.quality_score.overall_score)
                else:
                    older_data.append(data.quality_score.overall_score)

        trends = {}
        if recent_data and older_data:
            recent_avg = statistics.mean(recent_data)
            older_avg = statistics.mean(older_data)
            trends['score_change'] = recent_avg - older_avg
            trends['trend'] = 'improving' if recent_avg > older_avg else 'declining'
        else:
            trends['score_change'] = 0
            trends['trend'] = 'stable'

        return trends

    def blacklist_proxy(self, proxy_id: str, reason: str = "manual"):
        """将代理加入黑名单"""
        with self.lock:
            if proxy_id in self.quality_data:
                data = self.quality_data[proxy_id]
                data.quality_score.overall_score = 0
                logger.warning(f"Proxy {data.proxy_address} blacklisted: {reason}")

    def whitelist_proxy(self, proxy_id: str):
        """将代理从黑名单移除"""
        with self.lock:
            if proxy_id in self.quality_data:
                data = self.quality_data[proxy_id]
                data._recalculate_quality_score()
                logger.info(f"Proxy {data.proxy_address} removed from blacklist")

    def export_quality_data(self) -> Dict[str, Any]:
        """导出质量数据"""
        with self.lock:
            export_data = {}
            for proxy_id, data in self.quality_data.items():
                export_data[proxy_id] = {
                    'proxy_address': data.proxy_address,
                    'total_requests': data.total_requests,
                    'success_rate': data.success_rate,
                    'avg_response_time': data.avg_response_time,
                    'quality_score': asdict(data.quality_score),
                    'grade': data.quality_score.grade.value,
                    'is_healthy': data.is_healthy,
                    'domain_stats': data.domain_stats,
                    'geographic_info': {
                        'country': data.country,
                        'region': data.region,
                        'isp': data.isp
                    }
                }

            return {
                'timestamp': time.time(),
                'total_proxies': len(export_data),
                'proxies': export_data,
                'summary': self.get_quality_report()
            }

# 全局质量管理器实例
_quality_manager = None
_quality_lock = threading.Lock()

def get_quality_manager() -> ProxyQualityManager:
    """获取质量管理器实例（单例模式）"""
    global _quality_manager
    if _quality_manager is None:
        with _quality_lock:
            if _quality_manager is None:
                _quality_manager = ProxyQualityManager()
    return _quality_manager

def record_proxy_quality(proxy_id: str, proxy_address: str, success: bool,
                        response_time: float = 0.0, domain: str = None):
    """记录代理质量数据"""
    manager = get_quality_manager()
    manager.record_proxy_usage(proxy_id, proxy_address, success, response_time, domain)

def get_proxy_quality_grade(proxy_id: str) -> Optional[ProxyGrade]:
    """获取代理质量等级"""
    manager = get_quality_manager()
    return manager.get_proxy_grade(proxy_id)

def get_best_quality_proxies(count: int = 10, domain: str = None) -> List[Tuple[str, float]]:
    """获取最佳质量代理"""
    manager = get_quality_manager()
    return manager.get_best_proxies(count, domain)

def get_quality_report() -> Dict[str, Any]:
    """获取质量报告"""
    manager = get_quality_manager()
    return manager.get_quality_report()
