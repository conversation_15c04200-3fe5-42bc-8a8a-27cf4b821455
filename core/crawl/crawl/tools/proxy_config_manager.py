# -*- coding: utf-8 -*-

"""
代理配置管理系统
提供统一的代理配置管理，支持动态配置和环境隔离
"""

import os
import json
import yaml
import logging
import threading
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum

logger = logging.getLogger(__name__)

class Environment(Enum):
    """环境类型"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

class ProxyStrategy(Enum):
    """代理策略"""
    SMART = "smart"           # 智能选择
    ROUND_ROBIN = "round_robin"  # 轮询
    RANDOM = "random"         # 随机
    WEIGHTED = "weighted"     # 加权

@dataclass
class ProxyProviderConfig:
    """代理提供者配置"""
    name: str
    type: str  # api, file, database
    url: str = ""
    timeout: int = 10
    weight: float = 1.0
    enabled: bool = True
    auth: Dict[str, str] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    params: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ProxyPoolConfig:
    """代理池配置"""
    max_pool_size: int = 100
    min_pool_size: int = 10
    check_interval: int = 300
    validation_timeout: int = 5
    max_consecutive_failures: int = 3
    enable_quality_management: bool = True
    enable_monitoring: bool = True
    selector_strategy: ProxyStrategy = ProxyStrategy.SMART

@dataclass
class QualityConfig:
    """质量管理配置"""
    min_requests_for_grading: int = 10
    quality_check_interval: int = 300
    blacklist_threshold: int = 20
    cleanup_interval: int = 3600
    grade_weights: Dict[str, float] = field(default_factory=lambda: {
        'success_rate': 0.35,
        'response_time': 0.25,
        'stability': 0.20,
        'domain_compatibility': 0.15,
        'geographic_coverage': 0.05
    })

@dataclass
class MonitoringConfig:
    """监控配置"""
    enabled: bool = True
    interval: int = 60
    log_file: str = ""
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'success_rate': 0.7,
        'response_time': 5.0,
        'active_proxies': 5,
        'failed_requests': 10,
        'quality_score': 60.0,
        'blacklist_rate': 0.1
    })
    alert_handlers: List[str] = field(default_factory=lambda: ['log'])

@dataclass
class ProxyConfig:
    """完整的代理配置"""
    environment: Environment = Environment.DEVELOPMENT
    providers: List[ProxyProviderConfig] = field(default_factory=list)
    pool: ProxyPoolConfig = field(default_factory=ProxyPoolConfig)
    quality: QualityConfig = field(default_factory=QualityConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    
    # 中间件配置
    middleware_enabled: bool = True
    proxy_required: bool = False
    retry_times: int = 3
    retry_http_codes: List[int] = field(default_factory=lambda: [500, 502, 503, 504, 408, 429])
    
    # 排除配置
    exclude_spiders: List[str] = field(default_factory=list)
    exclude_domains: List[str] = field(default_factory=list)

class ProxyConfigManager:
    """代理配置管理器"""
    
    def __init__(self, config_dir: str = None, environment: str = None):
        self.config_dir = Path(config_dir or self._get_default_config_dir())
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.environment = Environment(environment or os.getenv('PROXY_ENV', 'development'))
        self.config: ProxyConfig = ProxyConfig(environment=self.environment)
        self.lock = threading.Lock()
        
        # 配置文件路径
        self.config_file = self.config_dir / f"proxy_config_{self.environment.value}.json"
        self.global_config_file = self.config_dir / "proxy_config_global.json"
        
        # 加载配置
        self._load_config()
        
        logger.info(f"ProxyConfigManager initialized for environment: {self.environment.value}")
    
    def _get_default_config_dir(self) -> str:
        """获取默认配置目录"""
        return os.path.join(os.path.dirname(__file__), '..', 'configs', 'proxy')
    
    def _load_config(self):
        """加载配置"""
        try:
            # 先加载全局配置
            if self.global_config_file.exists():
                with open(self.global_config_file, 'r', encoding='utf-8') as f:
                    global_data = json.load(f)
                    self._merge_config(global_data)
            
            # 再加载环境特定配置
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    env_data = json.load(f)
                    self._merge_config(env_data)
            else:
                # 如果环境配置不存在，创建默认配置
                self._create_default_config()
            
            # 从环境变量覆盖配置
            self._load_from_env()
            
        except Exception as e:
            logger.error(f"Error loading proxy config: {e}")
            self._create_default_config()
    
    def _merge_config(self, data: Dict[str, Any]):
        """合并配置数据"""
        try:
            # 合并提供者配置
            if 'providers' in data:
                providers = []
                for provider_data in data['providers']:
                    provider = ProxyProviderConfig(**provider_data)
                    providers.append(provider)
                self.config.providers = providers
            
            # 合并池配置
            if 'pool' in data:
                pool_data = data['pool']
                if 'selector_strategy' in pool_data:
                    pool_data['selector_strategy'] = ProxyStrategy(pool_data['selector_strategy'])
                self.config.pool = ProxyPoolConfig(**pool_data)
            
            # 合并质量配置
            if 'quality' in data:
                self.config.quality = QualityConfig(**data['quality'])
            
            # 合并监控配置
            if 'monitoring' in data:
                self.config.monitoring = MonitoringConfig(**data['monitoring'])
            
            # 合并其他配置
            for key in ['middleware_enabled', 'proxy_required', 'retry_times', 
                       'retry_http_codes', 'exclude_spiders', 'exclude_domains']:
                if key in data:
                    setattr(self.config, key, data[key])
                    
        except Exception as e:
            logger.error(f"Error merging config data: {e}")
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 代理URL配置
        proxy_urls = []
        if os.getenv('BB_PROXY_URL'):
            proxy_urls.append(os.getenv('BB_PROXY_URL'))
        if os.getenv('BB_PROXY_URL2'):
            proxy_urls.append(os.getenv('BB_PROXY_URL2'))
        
        # 如果环境变量中有代理URL，更新提供者配置
        if proxy_urls:
            providers = []
            for i, url in enumerate(proxy_urls):
                provider = ProxyProviderConfig(
                    name=f"env_provider_{i+1}",
                    type="api",
                    url=url,
                    enabled=True
                )
                providers.append(provider)
            
            # 如果没有配置的提供者，使用环境变量的
            if not self.config.providers:
                self.config.providers = providers
        
        # 其他环境变量配置
        if os.getenv('BB_SCRAPY_PROXY'):
            self.config.middleware_enabled = os.getenv('BB_SCRAPY_PROXY').lower() in ('true', '1', 'yes')
        
        if os.getenv('PROXY_REQUIRED'):
            self.config.proxy_required = os.getenv('PROXY_REQUIRED').lower() in ('true', '1', 'yes')
    
    def _create_default_config(self):
        """创建默认配置"""
        # 默认提供者配置
        default_providers = [
            ProxyProviderConfig(
                name="default_api",
                type="api",
                url=os.getenv('BB_PROXY_URL', 'http://47.92.30.85:5010'),
                timeout=10,
                weight=1.0,
                enabled=True
            )
        ]
        
        self.config = ProxyConfig(
            environment=self.environment,
            providers=default_providers,
            pool=ProxyPoolConfig(),
            quality=QualityConfig(),
            monitoring=MonitoringConfig()
        )
        
        # 保存默认配置
        self.save_config()
    
    def get_config(self) -> ProxyConfig:
        """获取当前配置"""
        with self.lock:
            return self.config
    
    def update_config(self, updates: Dict[str, Any]):
        """更新配置"""
        with self.lock:
            self._merge_config(updates)
            self.save_config()
            logger.info("Proxy configuration updated")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            config_data = asdict(self.config)
            
            # 处理枚举类型
            config_data['environment'] = self.config.environment.value
            config_data['pool']['selector_strategy'] = self.config.pool.selector_strategy.value
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def get_provider_configs(self) -> List[ProxyProviderConfig]:
        """获取启用的提供者配置"""
        with self.lock:
            return [p for p in self.config.providers if p.enabled]
    
    def add_provider(self, provider: ProxyProviderConfig):
        """添加提供者"""
        with self.lock:
            self.config.providers.append(provider)
            self.save_config()
            logger.info(f"Added provider: {provider.name}")
    
    def remove_provider(self, provider_name: str):
        """移除提供者"""
        with self.lock:
            self.config.providers = [
                p for p in self.config.providers 
                if p.name != provider_name
            ]
            self.save_config()
            logger.info(f"Removed provider: {provider_name}")
    
    def set_environment(self, environment: str):
        """切换环境"""
        new_env = Environment(environment)
        if new_env != self.environment:
            self.environment = new_env
            self.config_file = self.config_dir / f"proxy_config_{self.environment.value}.json"
            self._load_config()
            logger.info(f"Switched to environment: {environment}")
    
    def export_config(self) -> Dict[str, Any]:
        """导出配置"""
        with self.lock:
            return asdict(self.config)
    
    def validate_config(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 检查提供者配置
        if not self.config.providers:
            errors.append("No proxy providers configured")
        
        for provider in self.config.providers:
            if not provider.url and provider.type == 'api':
                errors.append(f"Provider {provider.name} missing URL")
        
        # 检查池配置
        if self.config.pool.min_pool_size > self.config.pool.max_pool_size:
            errors.append("min_pool_size cannot be greater than max_pool_size")
        
        # 检查质量配置
        if sum(self.config.quality.grade_weights.values()) != 1.0:
            errors.append("Quality grade weights must sum to 1.0")
        
        return errors

# 全局配置管理器实例
_config_manager = None
_config_lock = threading.Lock()

def get_config_manager() -> ProxyConfigManager:
    """获取配置管理器实例（单例模式）"""
    global _config_manager
    if _config_manager is None:
        with _config_lock:
            if _config_manager is None:
                _config_manager = ProxyConfigManager()
    return _config_manager

def get_proxy_config() -> ProxyConfig:
    """获取代理配置"""
    manager = get_config_manager()
    return manager.get_config()

def update_proxy_config(updates: Dict[str, Any]):
    """更新代理配置"""
    manager = get_config_manager()
    manager.update_config(updates)
