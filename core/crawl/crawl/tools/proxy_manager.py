# -*- coding: utf-8 -*-

"""
代理管理器 - 集成所有代理相关功能
提供统一的代理管理接口，整合代理池、质量管理、监控和配置管理
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import asdict

from .proxy_new import ProxyPoolManager, get_proxy_pool_manager
from .proxy_quality_manager import ProxyQualityManager, get_quality_manager
from .proxy_monitor import ProxyMonitor, get_proxy_monitor
from .proxy_config_manager import ProxyConfigManager, get_config_manager

logger = logging.getLogger(__name__)

class IntegratedProxyManager:
    """集成代理管理器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.pool_manager = None
        self.quality_manager = None
        self.monitor = None
        self.lock = threading.Lock()
        
        # 初始化组件
        self._initialize_components()
        
        logger.info("Integrated Proxy Manager initialized")
    
    def _initialize_components(self):
        """初始化各个组件"""
        try:
            config = self.config_manager.get_config()
            
            # 初始化代理池管理器
            pool_config = asdict(config.pool)
            pool_config['api_urls'] = [p.url for p in config.providers if p.enabled and p.url]
            pool_config['selector_type'] = config.pool.selector_strategy.value
            
            self.pool_manager = ProxyPoolManager(pool_config)
            
            # 初始化质量管理器
            quality_config = asdict(config.quality)
            self.quality_manager = ProxyQualityManager(quality_config)
            
            # 初始化监控器
            if config.monitoring.enabled:
                monitor_config = config.monitoring.log_file or None
                self.monitor = ProxyMonitor(monitor_config)
                
                # 设置告警阈值
                for metric, threshold in config.monitoring.alert_thresholds.items():
                    self.monitor.set_threshold(metric, threshold)
                
                # 启动监控
                self.monitor.start_monitoring(config.monitoring.interval)
            
            logger.info("All proxy components initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing proxy components: {e}")
            raise
    
    def get_proxy(self, target_url: str = None) -> Optional[str]:
        """获取代理"""
        if not self.pool_manager:
            logger.warning("Pool manager not initialized")
            return None
        
        proxy = self.pool_manager.get_proxy(target_url)
        
        # 记录监控数据
        if self.monitor:
            self.monitor.record_request(success=proxy is not None)
        
        return proxy
    
    def report_proxy_success(self, proxy: str, response_time: float, target_url: str = None):
        """报告代理使用成功"""
        if self.pool_manager:
            self.pool_manager.report_success(proxy, response_time, target_url)
        
        if self.quality_manager:
            import hashlib
            proxy_id = hashlib.md5(proxy.encode()).hexdigest()
            domain = None
            if target_url:
                from urllib.parse import urlparse
                domain = urlparse(target_url).netloc
            self.quality_manager.record_proxy_usage(
                proxy_id, proxy, True, response_time, domain
            )
        
        if self.monitor:
            self.monitor.record_request(success=True, response_time=response_time)
    
    def report_proxy_failure(self, proxy: str, target_url: str = None):
        """报告代理使用失败"""
        if self.pool_manager:
            self.pool_manager.report_failure(proxy, target_url)
        
        if self.quality_manager:
            import hashlib
            proxy_id = hashlib.md5(proxy.encode()).hexdigest()
            domain = None
            if target_url:
                from urllib.parse import urlparse
                domain = urlparse(target_url).netloc
            self.quality_manager.record_proxy_usage(
                proxy_id, proxy, False, 0.0, domain
            )
        
        if self.monitor:
            self.monitor.record_request(success=False)
    
    def get_proxy_stats(self) -> Dict[str, Any]:
        """获取代理统计信息"""
        stats = {}
        
        if self.pool_manager:
            stats['pool'] = self.pool_manager.get_stats()
        
        if self.quality_manager:
            stats['quality'] = self.quality_manager.get_quality_report()
        
        if self.monitor:
            stats['monitoring'] = self.monitor.get_current_status()
            stats['alerts'] = self.monitor.get_alert_summary()
        
        return stats
    
    def get_best_proxies(self, count: int = 10, domain: str = None) -> List[Tuple[str, float]]:
        """获取最佳代理"""
        if self.quality_manager:
            return self.quality_manager.get_best_proxies(count, domain)
        return []
    
    def get_comprehensive_report(self) -> Dict[str, Any]:
        """获取综合报告"""
        report = {
            'timestamp': time.time(),
            'config': asdict(self.config_manager.get_config()),
            'stats': self.get_proxy_stats()
        }
        
        if self.monitor:
            report['monitoring'] = self.monitor.get_comprehensive_report()
        
        return report
    
    def update_config(self, updates: Dict[str, Any]):
        """更新配置并重新初始化组件"""
        with self.lock:
            self.config_manager.update_config(updates)
            
            # 停止现有监控
            if self.monitor and self.monitor.monitoring:
                self.monitor.stop_monitoring()
            
            # 重新初始化组件
            self._initialize_components()
            
            logger.info("Configuration updated and components reinitialized")
    
    def add_provider(self, name: str, url: str, provider_type: str = "api", **kwargs):
        """添加代理提供者"""
        from .proxy_config_manager import ProxyProviderConfig
        
        provider = ProxyProviderConfig(
            name=name,
            type=provider_type,
            url=url,
            **kwargs
        )
        
        self.config_manager.add_provider(provider)
        
        # 重新初始化池管理器
        with self.lock:
            config = self.config_manager.get_config()
            pool_config = asdict(config.pool)
            pool_config['api_urls'] = [p.url for p in config.providers if p.enabled and p.url]
            
            if self.pool_manager:
                from .proxy_new import APIProxyProvider
                new_provider = APIProxyProvider(url, kwargs.get('timeout', 10))
                self.pool_manager.add_provider(new_provider)
        
        logger.info(f"Added proxy provider: {name}")
    
    def remove_provider(self, name: str):
        """移除代理提供者"""
        self.config_manager.remove_provider(name)
        
        # 重新初始化组件
        with self.lock:
            self._initialize_components()
        
        logger.info(f"Removed proxy provider: {name}")
    
    def set_proxy_strategy(self, strategy: str):
        """设置代理选择策略"""
        if self.pool_manager:
            self.pool_manager.set_selector(strategy)
        
        # 更新配置
        updates = {
            'pool': {
                'selector_strategy': strategy
            }
        }
        self.config_manager.update_config(updates)
        
        logger.info(f"Proxy strategy set to: {strategy}")
    
    def blacklist_proxy(self, proxy: str, reason: str = "manual"):
        """将代理加入黑名单"""
        if self.quality_manager:
            import hashlib
            proxy_id = hashlib.md5(proxy.encode()).hexdigest()
            self.quality_manager.blacklist_proxy(proxy_id, reason)
        
        if self.pool_manager:
            self.pool_manager.remove_proxy(proxy)
        
        logger.info(f"Blacklisted proxy: {proxy} (reason: {reason})")
    
    def clear_failed_proxies(self):
        """清理失败的代理"""
        if self.pool_manager:
            self.pool_manager.clear_failed_proxies()
        
        logger.info("Cleared failed proxies")
    
    def export_data(self) -> Dict[str, Any]:
        """导出所有数据"""
        data = {
            'timestamp': time.time(),
            'config': self.config_manager.export_config(),
            'pool_stats': self.pool_manager.get_stats() if self.pool_manager else {},
            'quality_data': self.quality_manager.export_quality_data() if self.quality_manager else {},
            'monitoring_data': self.monitor.export_monitoring_data() if self.monitor else {}
        }
        
        return data
    
    def validate_setup(self) -> List[str]:
        """验证设置"""
        errors = []
        
        # 验证配置
        config_errors = self.config_manager.validate_config()
        errors.extend(config_errors)
        
        # 验证组件状态
        if not self.pool_manager:
            errors.append("Pool manager not initialized")
        
        if not self.quality_manager:
            errors.append("Quality manager not initialized")
        
        config = self.config_manager.get_config()
        if config.monitoring.enabled and not self.monitor:
            errors.append("Monitoring enabled but monitor not initialized")
        
        return errors
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        status = {
            'overall': 'healthy',
            'components': {},
            'errors': []
        }
        
        # 检查各组件状态
        if self.pool_manager:
            pool_stats = self.pool_manager.get_stats()
            status['components']['pool'] = {
                'status': 'healthy' if pool_stats.get('active_proxies', 0) > 0 else 'warning',
                'active_proxies': pool_stats.get('active_proxies', 0),
                'total_proxies': pool_stats.get('total_proxies', 0)
            }
        else:
            status['components']['pool'] = {'status': 'error', 'message': 'Not initialized'}
            status['errors'].append('Pool manager not available')
        
        if self.quality_manager:
            quality_report = self.quality_manager.get_quality_report()
            avg_score = quality_report.get('average_scores', {}).get('overall', {}).get('mean', 0)
            status['components']['quality'] = {
                'status': 'healthy' if avg_score >= 60 else 'warning',
                'average_score': avg_score
            }
        else:
            status['components']['quality'] = {'status': 'error', 'message': 'Not initialized'}
        
        if self.monitor:
            status['components']['monitoring'] = {
                'status': 'healthy' if self.monitor.monitoring else 'warning',
                'is_monitoring': self.monitor.monitoring
            }
        
        # 确定整体状态
        component_statuses = [comp.get('status', 'error') for comp in status['components'].values()]
        if 'error' in component_statuses:
            status['overall'] = 'error'
        elif 'warning' in component_statuses:
            status['overall'] = 'warning'
        
        return status

# 全局集成管理器实例
_integrated_manager = None
_manager_lock = threading.Lock()

def get_integrated_proxy_manager() -> IntegratedProxyManager:
    """获取集成代理管理器实例（单例模式）"""
    global _integrated_manager
    if _integrated_manager is None:
        with _manager_lock:
            if _integrated_manager is None:
                _integrated_manager = IntegratedProxyManager()
    return _integrated_manager

# 便捷函数
def get_proxy(target_url: str = None) -> Optional[str]:
    """获取代理"""
    manager = get_integrated_proxy_manager()
    return manager.get_proxy(target_url)

def report_proxy_success(proxy: str, response_time: float, target_url: str = None):
    """报告代理成功"""
    manager = get_integrated_proxy_manager()
    manager.report_proxy_success(proxy, response_time, target_url)

def report_proxy_failure(proxy: str, target_url: str = None):
    """报告代理失败"""
    manager = get_integrated_proxy_manager()
    manager.report_proxy_failure(proxy, target_url)
