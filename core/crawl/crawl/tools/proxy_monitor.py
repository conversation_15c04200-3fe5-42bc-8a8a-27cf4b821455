# -*- coding: utf-8 -*-

"""
代理监控工具
提供代理池状态监控、性能分析、健康检查等功能
"""

import time
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
from enum import Enum
from pathlib import Path

from .proxy_new import get_proxy_pool_manager, get_proxy_stats
from .proxy_quality_manager import get_quality_manager, get_quality_report

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class Alert:
    """告警信息"""
    level: AlertLevel
    message: str
    timestamp: float = field(default_factory=time.time)
    metric: str = ""
    value: Any = None
    threshold: Any = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'level': self.level.value,
            'message': self.message,
            'timestamp': self.timestamp,
            'datetime': datetime.fromtimestamp(self.timestamp).isoformat(),
            'metric': self.metric,
            'value': self.value,
            'threshold': self.threshold
        }

class AlertManager:
    """告警管理器"""

    def __init__(self, max_alerts: int = 1000):
        self.alerts: deque = deque(maxlen=max_alerts)
        self.alert_handlers: List[callable] = []
        self.lock = threading.Lock()

        # 告警抑制配置
        self.suppression_rules = {
            'duplicate_window': 300,  # 5分钟内相同告警只发送一次
            'max_alerts_per_minute': 10  # 每分钟最多发送10个告警
        }

        self.recent_alerts = deque(maxlen=100)

    def add_alert_handler(self, handler: callable):
        """添加告警处理器"""
        self.alert_handlers.append(handler)

    def send_alert(self, alert: Alert):
        """发送告警"""
        with self.lock:
            # 检查告警抑制
            if self._should_suppress_alert(alert):
                return

            self.alerts.append(alert)
            self.recent_alerts.append((alert.message, time.time()))

            # 调用告警处理器
            for handler in self.alert_handlers:
                try:
                    handler(alert)
                except Exception as e:
                    logger.error(f"Alert handler error: {e}")

    def _should_suppress_alert(self, alert: Alert) -> bool:
        """检查是否应该抑制告警"""
        current_time = time.time()

        # 检查重复告警
        duplicate_window = self.suppression_rules['duplicate_window']
        for existing_alert in reversed(self.alerts):
            if current_time - existing_alert.timestamp > duplicate_window:
                break
            if (existing_alert.message == alert.message and
                existing_alert.metric == alert.metric):
                return True

        # 检查告警频率
        max_per_minute = self.suppression_rules['max_alerts_per_minute']
        recent_count = sum(
            1 for _, timestamp in self.recent_alerts
            if current_time - timestamp < 60
        )

        return recent_count >= max_per_minute

    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        cutoff_time = time.time() - (hours * 3600)
        with self.lock:
            return [
                alert.to_dict() for alert in self.alerts
                if alert.timestamp >= cutoff_time
            ]

    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        with self.lock:
            if not self.alerts:
                return {'total': 0, 'by_level': {}, 'recent_count': 0}

            by_level = defaultdict(int)
            recent_count = 0
            current_time = time.time()

            for alert in self.alerts:
                by_level[alert.level.value] += 1
                if current_time - alert.timestamp < 3600:  # 最近1小时
                    recent_count += 1

            return {
                'total': len(self.alerts),
                'by_level': dict(by_level),
                'recent_count': recent_count
            }

@dataclass
class ProxyMetrics:
    """代理指标数据"""
    timestamp: float
    total_proxies: int
    active_proxies: int
    average_success_rate: float
    average_response_time: float
    requests_per_minute: int
    failed_requests: int
    slow_requests: int

class ProxyMonitor:
    """代理监控器"""
    
    def __init__(self, log_file: str = None):
        self.metrics_history: List[ProxyMetrics] = []
        self.log_file = log_file or f"proxy_monitor_{datetime.now().strftime('%Y%m%d')}.json"
        self.monitoring = False
        self.monitor_thread = None
        self.interval = 60  # 监控间隔（秒）

        # 告警管理器
        self.alert_manager = AlertManager()
        self._setup_alert_handlers()

        # 性能阈值
        self.warning_thresholds = {
            'success_rate': 0.7,      # 成功率警告阈值
            'response_time': 5.0,     # 响应时间警告阈值（秒）
            'active_proxies': 5,      # 活跃代理数量警告阈值
            'failed_requests': 10,    # 失败请求警告阈值
            'quality_score': 60.0,    # 质量评分警告阈值
            'blacklist_rate': 0.1     # 黑名单比例警告阈值
        }

        # 统计计数器
        self.request_counter = 0
        self.failed_counter = 0
        self.slow_counter = 0
        self.last_reset_time = time.time()

        # 质量监控
        self.quality_manager = get_quality_manager()

    def _setup_alert_handlers(self):
        """设置告警处理器"""
        # 日志告警处理器
        def log_alert_handler(alert: Alert):
            log_level = {
                AlertLevel.INFO: logger.info,
                AlertLevel.WARNING: logger.warning,
                AlertLevel.ERROR: logger.error,
                AlertLevel.CRITICAL: logger.critical
            }.get(alert.level, logger.info)

            log_level(f"PROXY ALERT [{alert.level.value.upper()}]: {alert.message}")

        self.alert_manager.add_alert_handler(log_alert_handler)

        # 可以添加更多处理器，如邮件、钉钉、Slack等
        # self.alert_manager.add_alert_handler(email_alert_handler)
        # self.alert_manager.add_alert_handler(slack_alert_handler)
    
    def start_monitoring(self, interval: int = 60):
        """开始监控"""
        if self.monitoring:
            logger.warning("Proxy monitoring is already running")
            return
        
        self.interval = interval
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
        self.monitor_thread.start()
        logger.info(f"Proxy monitoring started with {interval}s interval")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Proxy monitoring stopped")
    
    def _monitor_worker(self):
        """监控工作线程"""
        while self.monitoring:
            try:
                self._collect_metrics()
                self._check_alerts()
                self._save_metrics()
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"Error in monitor worker: {e}")
                time.sleep(10)
    
    def _collect_metrics(self):
        """收集代理指标"""
        try:
            stats = get_proxy_stats()
            current_time = time.time()
            
            # 计算请求统计
            time_diff = current_time - self.last_reset_time
            requests_per_minute = int(self.request_counter / (time_diff / 60)) if time_diff > 0 else 0
            
            # 计算平均响应时间
            proxy_details = stats.get('proxy_details', [])
            response_times = [p.get('response_time', 0) for p in proxy_details if p.get('response_time', 0) > 0]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            metrics = ProxyMetrics(
                timestamp=current_time,
                total_proxies=stats.get('total_proxies', 0),
                active_proxies=stats.get('active_proxies', 0),
                average_success_rate=stats.get('average_success_rate', 0),
                average_response_time=avg_response_time,
                requests_per_minute=requests_per_minute,
                failed_requests=self.failed_counter,
                slow_requests=self.slow_counter
            )
            
            self.metrics_history.append(metrics)
            
            # 保持历史记录在合理范围内（保留最近24小时的数据）
            max_history = 24 * 60 // self.interval  # 24小时的数据点数量
            if len(self.metrics_history) > max_history:
                self.metrics_history = self.metrics_history[-max_history:]
            
            # 重置计数器
            self.request_counter = 0
            self.failed_counter = 0
            self.slow_counter = 0
            self.last_reset_time = current_time
            
            logger.debug(f"Collected metrics: {asdict(metrics)}")
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
    
    def _check_alerts(self):
        """检查告警条件"""
        if not self.metrics_history:
            return

        latest = self.metrics_history[-1]

        # 检查成功率
        if latest.average_success_rate < self.warning_thresholds['success_rate']:
            alert = Alert(
                level=AlertLevel.WARNING,
                message=f"Low proxy success rate: {latest.average_success_rate:.2%}",
                metric="success_rate",
                value=latest.average_success_rate,
                threshold=self.warning_thresholds['success_rate']
            )
            self.alert_manager.send_alert(alert)

        # 检查响应时间
        if latest.average_response_time > self.warning_thresholds['response_time']:
            alert = Alert(
                level=AlertLevel.WARNING,
                message=f"High proxy response time: {latest.average_response_time:.2f}s",
                metric="response_time",
                value=latest.average_response_time,
                threshold=self.warning_thresholds['response_time']
            )
            self.alert_manager.send_alert(alert)

        # 检查活跃代理数量
        if latest.active_proxies < self.warning_thresholds['active_proxies']:
            level = AlertLevel.CRITICAL if latest.active_proxies == 0 else AlertLevel.WARNING
            alert = Alert(
                level=level,
                message=f"Low active proxy count: {latest.active_proxies}",
                metric="active_proxies",
                value=latest.active_proxies,
                threshold=self.warning_thresholds['active_proxies']
            )
            self.alert_manager.send_alert(alert)

        # 检查失败请求
        if latest.failed_requests > self.warning_thresholds['failed_requests']:
            alert = Alert(
                level=AlertLevel.ERROR,
                message=f"High failed request count: {latest.failed_requests}",
                metric="failed_requests",
                value=latest.failed_requests,
                threshold=self.warning_thresholds['failed_requests']
            )
            self.alert_manager.send_alert(alert)

        # 检查质量评分
        self._check_quality_alerts()

    def _check_quality_alerts(self):
        """检查质量相关告警"""
        try:
            quality_report = get_quality_report()

            if not quality_report or quality_report.get('total_proxies', 0) == 0:
                return

            # 检查平均质量评分
            overall_scores = quality_report.get('average_scores', {}).get('overall', {})
            if overall_scores:
                avg_score = overall_scores.get('mean', 0)
                if avg_score < self.warning_thresholds['quality_score']:
                    alert = Alert(
                        level=AlertLevel.WARNING,
                        message=f"Low average proxy quality score: {avg_score:.1f}",
                        metric="quality_score",
                        value=avg_score,
                        threshold=self.warning_thresholds['quality_score']
                    )
                    self.alert_manager.send_alert(alert)

            # 检查黑名单比例
            grade_dist = quality_report.get('grade_distribution', {})
            total_proxies = quality_report.get('total_proxies', 0)
            if total_proxies > 0:
                blacklist_count = grade_dist.get('blacklist', 0)
                blacklist_rate = blacklist_count / total_proxies

                if blacklist_rate > self.warning_thresholds['blacklist_rate']:
                    alert = Alert(
                        level=AlertLevel.ERROR,
                        message=f"High blacklist rate: {blacklist_rate:.2%} ({blacklist_count}/{total_proxies})",
                        metric="blacklist_rate",
                        value=blacklist_rate,
                        threshold=self.warning_thresholds['blacklist_rate']
                    )
                    self.alert_manager.send_alert(alert)

        except Exception as e:
            logger.error(f"Error checking quality alerts: {e}")
    
    def _save_metrics(self):
        """保存指标到文件"""
        try:
            metrics_data = [asdict(m) for m in self.metrics_history]
            
            # 确保目录存在
            log_path = Path(self.log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Error saving metrics: {e}")
    
    def record_request(self, success: bool = True, response_time: float = 0):
        """记录请求"""
        self.request_counter += 1
        
        if not success:
            self.failed_counter += 1
        
        if response_time > 10:  # 慢请求阈值
            self.slow_counter += 1
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        if not self.metrics_history:
            return {}
        
        latest = self.metrics_history[-1]
        return asdict(latest)
    
    def get_trend_analysis(self, hours: int = 1) -> Dict[str, Any]:
        """获取趋势分析"""
        if not self.metrics_history:
            return {}
        
        cutoff_time = time.time() - (hours * 3600)
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return {}
        
        # 计算趋势
        success_rates = [m.average_success_rate for m in recent_metrics]
        response_times = [m.average_response_time for m in recent_metrics]
        active_proxies = [m.active_proxies for m in recent_metrics]
        
        return {
            'period_hours': hours,
            'data_points': len(recent_metrics),
            'success_rate_trend': {
                'current': success_rates[-1] if success_rates else 0,
                'average': sum(success_rates) / len(success_rates) if success_rates else 0,
                'min': min(success_rates) if success_rates else 0,
                'max': max(success_rates) if success_rates else 0
            },
            'response_time_trend': {
                'current': response_times[-1] if response_times else 0,
                'average': sum(response_times) / len(response_times) if response_times else 0,
                'min': min(response_times) if response_times else 0,
                'max': max(response_times) if response_times else 0
            },
            'active_proxies_trend': {
                'current': active_proxies[-1] if active_proxies else 0,
                'average': sum(active_proxies) / len(active_proxies) if active_proxies else 0,
                'min': min(active_proxies) if active_proxies else 0,
                'max': max(active_proxies) if active_proxies else 0
            }
        }
    
    def generate_report(self) -> str:
        """生成监控报告"""
        current_status = self.get_current_status()
        trend_analysis = self.get_trend_analysis()
        
        report = f"""
代理池监控报告
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

当前状态:
- 总代理数: {current_status.get('total_proxies', 0)}
- 活跃代理数: {current_status.get('active_proxies', 0)}
- 平均成功率: {current_status.get('average_success_rate', 0):.2%}
- 平均响应时间: {current_status.get('average_response_time', 0):.2f}s
- 每分钟请求数: {current_status.get('requests_per_minute', 0)}
- 失败请求数: {current_status.get('failed_requests', 0)}
- 慢请求数: {current_status.get('slow_requests', 0)}

趋势分析 (最近1小时):
- 成功率趋势: {trend_analysis.get('success_rate_trend', {}).get('current', 0):.2%}
- 响应时间趋势: {trend_analysis.get('response_time_trend', {}).get('current', 0):.2f}s
- 活跃代理趋势: {trend_analysis.get('active_proxies_trend', {}).get('current', 0)}

告警阈值:
- 成功率警告: < {self.warning_thresholds['success_rate']:.2%}
- 响应时间警告: > {self.warning_thresholds['response_time']}s
- 活跃代理警告: < {self.warning_thresholds['active_proxies']}
- 失败请求警告: > {self.warning_thresholds['failed_requests']}
"""
        return report

    def get_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取告警信息"""
        return self.alert_manager.get_recent_alerts(hours)

    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        return self.alert_manager.get_alert_summary()

    def add_alert_handler(self, handler: callable):
        """添加自定义告警处理器"""
        self.alert_manager.add_alert_handler(handler)

    def set_threshold(self, metric: str, value: float):
        """设置告警阈值"""
        if metric in self.warning_thresholds:
            self.warning_thresholds[metric] = value
            logger.info(f"Updated threshold for {metric}: {value}")
        else:
            logger.warning(f"Unknown metric: {metric}")

    def get_comprehensive_report(self) -> Dict[str, Any]:
        """获取综合监控报告"""
        basic_report = self.generate_report()
        quality_report = get_quality_report()
        alert_summary = self.get_alert_summary()
        recent_alerts = self.get_alerts(24)

        return {
            'timestamp': time.time(),
            'basic_metrics': basic_report,
            'quality_metrics': quality_report,
            'alert_summary': alert_summary,
            'recent_alerts': recent_alerts,
            'thresholds': self.warning_thresholds,
            'monitoring_status': {
                'is_monitoring': self.monitoring,
                'interval': self.interval,
                'uptime': time.time() - self.last_reset_time if self.monitoring else 0
            }
        }

    def export_monitoring_data(self, include_history: bool = True) -> Dict[str, Any]:
        """导出监控数据"""
        data = {
            'export_timestamp': time.time(),
            'config': {
                'interval': self.interval,
                'thresholds': self.warning_thresholds
            },
            'current_status': self.get_current_status(),
            'alert_summary': self.get_alert_summary(),
            'quality_report': get_quality_report()
        }

        if include_history:
            data['metrics_history'] = [asdict(m) for m in self.metrics_history]
            data['recent_alerts'] = self.get_alerts(168)  # 一周的告警

        return data

# 全局监控器实例
_monitor = None

def get_proxy_monitor() -> ProxyMonitor:
    """获取代理监控器实例"""
    global _monitor
    if _monitor is None:
        _monitor = ProxyMonitor()
    return _monitor

def start_proxy_monitoring(interval: int = 60):
    """启动代理监控"""
    monitor = get_proxy_monitor()
    monitor.start_monitoring(interval)

def stop_proxy_monitoring():
    """停止代理监控"""
    monitor = get_proxy_monitor()
    monitor.stop_monitoring()

def record_proxy_request(success: bool = True, response_time: float = 0):
    """记录代理请求"""
    monitor = get_proxy_monitor()
    monitor.record_request(success, response_time)

def get_proxy_monitor_report() -> str:
    """获取代理监控报告"""
    monitor = get_proxy_monitor()
    return monitor.generate_report() 