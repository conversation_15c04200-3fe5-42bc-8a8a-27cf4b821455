# -*- coding: utf-8 -*-

"""
财联社新闻爬虫
爬取财联社(CLS)的电报新闻数据
数据源：www.cls.cn
"""

import time
import re

from crawl.items import FbeItem
from crawl.tools.spider_base import NewsSpider, SpiderConfig
from crawl.tools.performance import timing_decorator
from crawl.tools.data_processor import create_news_processor
from crawl.tools.helper import get_first_cookies, random_user_agent
from crawl.tools.cls_request_generator import get_cls_request_generator
from crawl.tools.cls_session_manager import get_cls_session_manager
from scrapy.http import FormRequest


class ClsSpider(NewsSpider):
    """
    财联社新闻爬虫
    爬取财联社(CLS)的电报新闻数据
    """
    name = 'cls'
    allowed_domains = ['www.cls.cn']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data_processor = create_news_processor()

    def get_spider_config(self) -> SpiderConfig:
        """获取爬虫配置 - 财联社需要更保守的配置"""
        return SpiderConfig(
            concurrent_requests=1,
            download_delay=2,
            batch_size=15,
            retry_times=2
        )

    def get_start_urls(self) -> list:
        """获取起始URL列表"""
        return []  # 使用start_requests方法



    def _get_cookies_with_retry(self, max_retries=3):
        """获取cookies，带重试机制"""
        for attempt in range(max_retries):
            try:
                cookies = get_first_cookies('https://www.cls.cn/')
                if cookies:
                    self.logger.debug(f"Successfully got cookies on attempt {attempt + 1}")
                    return cookies
            except Exception as e:
                self.logger.warning(f"Failed to get cookies on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)  # 等待1秒后重试

        self.logger.warning("Failed to get cookies after all retries, using empty dict")
        return {}



    def start_requests(self):
        """生成初始请求"""
        # 初始化会话管理器
        session_manager = get_cls_session_manager()

        # 刷新会话以确保有效访问
        session_refreshed = session_manager.refresh_session()
        if not session_refreshed:
            self.logger.warning("Failed to refresh session, proceeding with fallback")

        # 查找可用的API端点
        working_endpoint = session_manager.find_working_endpoint()
        if working_endpoint:
            self.logger.info(f"Using working endpoint: {working_endpoint}")

        # 使用请求生成器创建多个请求，提高成功率
        generator = get_cls_request_generator()
        requests = generator.generate_multiple_requests(count=3)

        # 获取有效的cookies和headers
        valid_cookies = session_manager.get_valid_cookies()
        api_headers = session_manager.get_api_headers()

        for i, request_data in enumerate(requests):
            # 合并会话管理器的headers和请求生成器的headers
            merged_headers = api_headers.copy()
            merged_headers.update(request_data['headers'])

            # 如果有可用端点，使用它替换URL中的域名部分
            url = request_data['url']
            if working_endpoint and working_endpoint != "https://www.cls.cn/nodeapi/telegraphList":
                from urllib.parse import urlparse, parse_qs, urlencode
                parsed_original = urlparse(request_data['url'])
                parsed_working = urlparse(working_endpoint)

                # 构建新URL
                url = f"{parsed_working.scheme}://{parsed_working.netloc}{parsed_working.path}?{parsed_original.query}"

            yield FormRequest(
                url=url,
                cookies=valid_cookies,
                headers=merged_headers,
                callback=self.parse_response,
                dont_filter=True,
                meta={
                    'handle_httpstatus_list': [302, 403, 429, 503],  # 处理这些状态码
                    'download_timeout': 30,
                    'retry_times': 0,
                    'request_index': i,  # 标记请求索引
                    'total_requests': len(requests),
                    'session_manager': session_manager  # 传递会话管理器
                }
            )

    @timing_decorator('cls.parse_response')
    def parse_response(self, response):
        """解析财联社新闻响应数据"""
        try:
            # 首先检查响应是否有效
            if not self._is_valid_response(response):
                self.logger.warning(f"Invalid response detected from {response.url}")
                return

            # 尝试解析JSON
            json_data = self.parse_json_response(response)
            if not json_data:
                self.logger.warning(f"Failed to parse JSON from {response.url}")
                self._log_response_details(response)
                return

            # 检查响应结构
            if not self._validate_response_structure(json_data, response.url):
                return

            roll_data = json_data.get('data', {}).get('roll_data', [])
            if not roll_data:
                self.logger.info(f"No news data found in {response.url}")
                return

            # 处理新闻数据
            processed_count = 0
            for news in roll_data:
                if self._is_valid_news(news):
                    item = self._create_news_item(news)
                    if item:
                        yield item
                        processed_count += 1

            self.logger.info(f"Successfully processed {processed_count} news items from {response.url}")

        except Exception as e:
            self.handle_error(e, response)

    def _is_valid_response(self, response):
        """检查响应是否有效"""
        # 检查状态码
        if response.status != 200:
            self.logger.warning(f"Non-200 status code: {response.status}")
            return False

        # 检查内容长度
        if len(response.body) < 100:
            self.logger.warning(f"Response too short: {len(response.body)} bytes")
            return False

        # 检查是否是HTML错误页面
        response_text = response.text.lower()
        error_indicators = [
            '<title>net video system</title>',
            '<html><head><title>',
            'access denied',
            'forbidden',
            'not found',
            'service unavailable'
        ]

        for indicator in error_indicators:
            if indicator in response_text:
                self.logger.warning(f"Error page detected: {indicator}")
                return False

        # 检查是否看起来像JSON
        response_text_stripped = response.text.strip()
        if not (response_text_stripped.startswith('{') or response_text_stripped.startswith('[')):
            self.logger.warning("Response doesn't look like JSON")
            return False

        return True

    def _validate_response_structure(self, json_data, url):
        """验证响应数据结构"""
        # 使用请求生成器的验证方法
        generator = get_cls_request_generator()

        if not generator.validate_response(json_data):
            self.logger.warning(f"Response validation failed for {url}")
            self._log_response_structure(json_data)
            return False

        return True

    def _log_response_structure(self, json_data):
        """记录响应结构用于调试"""
        try:
            if isinstance(json_data, dict):
                keys = list(json_data.keys())
                self.logger.debug(f"Response keys: {keys}")

                if 'data' in json_data:
                    data = json_data['data']
                    if isinstance(data, dict):
                        data_keys = list(data.keys())
                        self.logger.debug(f"Data keys: {data_keys}")
                    else:
                        self.logger.debug(f"Data type: {type(data)}")
                else:
                    self.logger.debug("No 'data' field in response")
            else:
                self.logger.debug(f"Response type: {type(json_data)}")
        except Exception as e:
            self.logger.debug(f"Error logging response structure: {e}")

    def _log_response_details(self, response):
        """记录响应详情用于调试"""
        self.logger.debug(f"Response details for {response.url}:")
        self.logger.debug(f"  Status: {response.status}")
        self.logger.debug(f"  Headers: {dict(response.headers)}")
        self.logger.debug(f"  Body length: {len(response.body)}")

        # 记录响应内容的前500字符
        try:
            preview = response.text[:500]
            self.logger.debug(f"  Body preview: {preview}")
        except Exception as e:
            self.logger.debug(f"  Body preview error: {e}")

    def _is_valid_news(self, news_data: dict) -> bool:
        """验证新闻数据是否有效"""
        return (
            news_data.get('ctime') and
            news_data.get('content') and
            len(news_data.get('content', '').strip()) > 0
        )

    def _create_news_item(self, news_data: dict) -> dict:
        """创建新闻数据项"""
        try:
            # 解析时间
            ctime = news_data.get('ctime', 0)
            try:
                time_local = time.localtime(ctime)
                dt = time.strftime("%Y-%m-%d %H:%M:%S", time_local)
            except (ValueError, TypeError, OSError):
                dt = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

            # 清理内容 - 移除"财联社...讯，"前缀
            content = news_data.get('content', '')
            pattern = re.compile(r'财联社.*?讯，')
            cleaned_content = re.sub(pattern, '', content).strip()

            # 提取股票关键词
            keywords = []
            stock_list = news_data.get('stock_list', [])
            for stock in stock_list:
                stock_id = stock.get('StockID', '')
                if len(stock_id) >= 6:
                    keywords.append(stock_id[-6:])  # 取后6位作为股票代码

            # 创建数据项
            item = FbeItem()
            item['newsID'] = ctime
            item['time'] = dt
            item['content'] = cleaned_content
            item['Level'] = news_data.get('Level', '')
            item['source'] = 'cls'
            item['Keywords'] = ','.join(keywords)

            # 处理数据项
            processed_item = self.process_item(dict(item))
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating news item: {e}")
            return None