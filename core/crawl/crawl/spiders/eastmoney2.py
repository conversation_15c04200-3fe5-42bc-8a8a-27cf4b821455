# -*- coding: utf-8 -*-

"""
东方财富股票数据爬虫 - 日常版本
爬取所有股票的实时数据，适用于日常数据更新
相比eastmoney.py，此版本更轻量，适合频繁运行
"""

import random
import time
from typing import List

from crawl.items import EastMomenyItem
from crawl.tools.spider_base import DataSpider, SpiderConfig
from crawl.tools.performance import timing_decorator
from crawl.tools.eastmoney_optimizer import optimize_eastmoney_request, record_eastmoney_result

class Eastmoney2Spider(DataSpider):
    """
    东方财富股票数据爬虫 - 日常版本
    爬取所有股票的实时数据，适用于日常数据更新
    相比eastmoney.py，此版本更轻量，适合频繁运行
    """
    name = "eastmoney2"
    allowed_domains = ["eastmoney.com"]

    # 自定义设置：处理Redis去重问题
    custom_settings = {
        # 方案1: 完全禁用去重（适用于需要重复请求的场景）
        'DUPEFILTER_CLASS': 'scrapy.dupefilters.BaseDupeFilter',

        # 方案2: 使用内存去重而不是Redis去重（取消注释下面这行，注释上面这行）
        # 'DUPEFILTER_CLASS': 'scrapy.dupefilters.RFPDupeFilter',

        # 方案3: 清理Redis去重缓存（取消注释下面这行）
        # 'SCHEDULER_FLUSH_ON_START': True,
    }

    # 配置常量
    PAGE_SIZE = 100
    MAX_PAGES = 60
    VALID_MARKET_TYPES = [81, 80, 23, 6, 2]  # 有效的市场类型

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 硬编码hosts，更直观
        self.hosts = ['59', '60', '61', '62', '63']
        self.fields = self._generate_fields()

    def get_spider_config(self) -> SpiderConfig:
        """获取爬虫配置 - 反爬优化版本"""
        return SpiderConfig(
            concurrent_requests=2,  # 进一步降低并发数以避免反爬
            download_delay=3.0,     # 增加延迟到3秒
            batch_size=15,          # 更小的批次
            retry_times=5,          # 保持重试次数
            retry_backoff=True      # 启用退避策略
        )

    def _generate_fields(self) -> List[str]:
        """生成字段列表"""
        fields = [f'f{num}' for num in range(1, 51)]
        fields.extend(['f57', 'f62', 'f100', 'f102', 'f103', 'f127', 'f129', 'f149'])
        return fields

    def get_start_urls(self) -> List[str]:
        """生成起始URL列表 - 使用优化器"""
        urls = []
        fields_str = ",".join(self.fields)

        # 基础URL模板
        base_url_template = 'http://59.push2.eastmoney.com/api/qt/clist/get?pn={page}&pz={page_size}&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048&fields={fields}'

        for page in range(1, self.MAX_PAGES + 1):
            # 生成基础URL
            base_url = base_url_template.format(
                page=page,
                page_size=self.PAGE_SIZE,
                fields=fields_str
            )

            # 使用优化器优化URL
            optimized_url, headers, delay = optimize_eastmoney_request(base_url)
            urls.append(optimized_url)

            # 添加小延迟避免生成URL时过于频繁
            time.sleep(0.1)

        self.logger.info(f"Generated {len(urls)} optimized URLs")
        return urls

    @timing_decorator('eastmoney2.parse_response')
    def parse_response(self, response):
        """解析响应数据"""
        try:
            # 记录请求成功
            record_eastmoney_result(response.url, True)

            json_data = self.parse_json_response(response)
            if not json_data:
                self.logger.warning(f"Empty response from {response.url}")
                record_eastmoney_result(response.url, False, 'empty_response')
                return

            # 安全地获取股票数据，防止NoneType错误
            data_section = json_data.get('data', {})
            if data_section is None:
                data_section = {}
            stocks_data = data_section.get('diff', [])
            if not stocks_data:
                self.logger.info(f"No stock data found in {response.url}")
                # 尝试减少MAX_PAGES，如果发现后面的页面没有数据
                if 'pn=' in response.url:
                    page_num = int(response.url.split('pn=')[1].split('&')[0])
                    if page_num > 30 and self.MAX_PAGES > page_num:
                        self.logger.info(f"Reducing MAX_PAGES from {self.MAX_PAGES} to {page_num}")
                        self.MAX_PAGES = page_num
                return

            # 处理股票数据
            for stock in stocks_data:
                if self._is_valid_stock(stock):
                    item = self._create_stock_item(stock)
                    if item:
                        yield item

        except Exception as e:
            # 记录请求失败
            record_eastmoney_result(response.url, False, str(e))
            self.handle_error(e, response)

    def _is_valid_stock(self, stock_data: dict) -> bool:
        """验证股票数据是否有效"""
        if not stock_data:
            return False
        return bool(
            stock_data.get('f19') in self.VALID_MARKET_TYPES and
            stock_data.get('f12') and  # 股票代码不为空
            stock_data.get('f14')      # 股票名称不为空
        )

    def _create_stock_item(self, stock_data: dict) -> dict:
        """创建股票数据项 - 简化版本，直接映射字段"""
        try:
            item = EastMomenyItem()

            # 基础字段
            item['displayName'] = stock_data.get('f14')
            item['code'] = stock_data.get('f12')

            # 动态映射所有f字段
            for field in self.fields:
                if field in stock_data:
                    item[field] = stock_data[field]

            # 处理数据项
            processed_item = self.process_item(dict(item))
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating stock item: {e}")
            return None