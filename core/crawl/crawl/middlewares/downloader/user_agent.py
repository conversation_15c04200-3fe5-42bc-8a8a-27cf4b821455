import random
import time
import signal
import os
import warnings
from typing import Optional, Union, Dict, Any
from scrapy.http import Request, Response
from scrapy.spiders import Spider
from scrapy.exceptions import NotConfigured
from ..base import BaseMiddleware

# 尝试导入fake_useragent，如果失败则使用备用方案
FAKE_USERAGENT_AVAILABLE = False
try:
    from fake_useragent import UserAgent
    FAKE_USERAGENT_AVAILABLE = True
except ImportError:
    pass

class MockUserAgent:
    """模拟UserAgent类，用于当fake_useragent不可用时"""
    def __init__(self, *args, **kwargs):
        pass

    @property
    def random(self):
        return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'


class UserAgentMiddleware(BaseMiddleware):
    """随机用户代理中间件 - 迁移自middlewares_old.py"""

    def __init__(self, crawler=None):
        super().__init__(crawler)
        self._last_ua_refresh = time.time()
        self._ua_refresh_interval = 3600  # 1小时刷新一次
        self._init_user_agent()

    def _init_user_agent(self):
        """初始化用户代理"""
        self.ua = None

        # 更全面的备用用户代理列表
        self.fallback_user_agents = [
            # Chrome on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            # Chrome on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            # Chrome on Linux
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            # Firefox on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            # Firefox on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
            # Safari on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            # Edge on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
        ]

        if not FAKE_USERAGENT_AVAILABLE:
            self.logger.warning("fake_useragent library not available, using fallback user agents")
            return

        try:
            # 抑制fake_useragent的警告信息
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                # 尝试使用默认初始化（适用于0.1.11版本）
                self.ua = UserAgent()
                self.logger.info("User agent initialized successfully with default settings")
        except Exception as e:
            self.logger.warning(f"Failed to initialize UserAgent with default settings: {e}")
            try:
                # 尝试使用MockUserAgent作为最后的备用方案
                self.ua = MockUserAgent()
                self.logger.info("Using MockUserAgent as fallback")
            except Exception as e2:
                self.logger.error(f"Failed to initialize any UserAgent: {e2}, using fallback list")
                self.ua = None

    def _refresh_user_agent_if_needed(self):
        """如果需要，刷新用户代理"""
        current_time = time.time()
        if current_time - self._last_ua_refresh > self._ua_refresh_interval:
            self.logger.info("Refreshing user agent...")
            self._init_user_agent()
            self._last_ua_refresh = current_time

    def _get_user_agent(self) -> str:
        """获取用户代理"""
        # 定期刷新用户代理
        self._refresh_user_agent_if_needed()

        try:
            if self.ua and hasattr(self.ua, 'random'):
                # 抑制可能的警告
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    ua = self.ua.random
                    if ua and len(ua.strip()) > 0:
                        self.stats['user_agent_rotated'] = self.stats.get('user_agent_rotated', 0) + 1
                        return ua
                    else:
                        # 如果返回的UA为空，使用备用
                        self.logger.debug("Empty user agent returned, using fallback")
                        return random.choice(self.fallback_user_agents)
            else:
                # 使用备用用户代理
                return random.choice(self.fallback_user_agents)
        except Exception as e:
            self.logger.warning(f"Error getting user agent: {e}, using fallback")
            return random.choice(self.fallback_user_agents)

    def process_request(self, request: Request, spider: Spider) -> Optional[Request]:
        """处理请求，设置用户代理"""
        self.stats['requests_processed'] = self.stats.get('requests_processed', 0) + 1
        user_agent = self._get_user_agent()
        request.headers['User-Agent'] = user_agent
        self.logger.debug(f"Set User-Agent for {request.url}: {user_agent}")
        return None

    def process_response(self, request: Request, response: Response, spider: Spider) -> Union[Request, Response]:
        """处理响应"""
        self.stats['responses_processed'] = self.stats.get('responses_processed', 0) + 1
        return response

    def process_exception(self, request: Request, exception: Exception, spider: Spider) -> Optional[Request]:
        """处理异常"""
        self.stats['errors'] = self.stats.get('errors', 0) + 1
        self.logger.error(f"Error processing {request.url}: {exception}")
        return None

    def spider_opened(self, spider):
        """爬虫开启时的回调"""
        super().spider_opened(spider)
        ua_status = "fake_useragent" if self.ua and not isinstance(self.ua, MockUserAgent) else "fallback"
        self.logger.info(f"UserAgentMiddleware initialized with {ua_status} user agents for spider: {spider.name}")
        self.logger.info(f"Available fallback user agents: {len(self.fallback_user_agents)}")

    def spider_closed(self, spider):
        """爬虫关闭时的回调"""
        super().spider_closed(spider)
        self.logger.info(f"UserAgentMiddleware stats for {spider.name}: {self.get_stats()}")