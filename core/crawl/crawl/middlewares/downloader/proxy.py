import random
import time
import os
import signal
from typing import Optional, Union, List, Dict, Any
from scrapy.http import Request, Response
from scrapy.spiders import <PERSON>
from scrapy.exceptions import IgnoreRequest
from ..base import BaseMiddleware

# 导入代理相关工具函数 - 使用新的代理池管理器
from crawl.tools.proxy_new import (
    get_proxy_pool_manager,
    report_proxy_success,
    report_proxy_failure,
    get_proxy_stats,
    get_random_proxy
)


class ProxyMiddleware(BaseMiddleware):
    """代理中间件 - 迁移自middlewares_old.py的完整代理功能"""

    def __init__(self, crawler=None):
        super().__init__(crawler)
        self._init_proxy_settings()
        self.logger.info(f"ProxyMiddleware initialized with proxy settings: {self.use_proxy}")

    def _init_proxy_settings(self):
        """初始化代理设置"""
        try:
            self.proxy_spider_exclude = self.settings.get('PROXY_SPIDER_EXCLUDE', [])
            self.is_proxy_enabled = self._parse_bool(os.getenv('BB_SCRAPY_PROXY', 'True'))
            self.retry_times = self.settings.get('RETRY_TIMES', 2)
            self.retry_http_codes = self.settings.get('RETRY_HTTP_CODES', [500, 502, 503, 504, 408, 429])
            self.proxy_required = self.settings.getbool('PROXY_REQUIRED', False)
            self.proxy_timeout = self.settings.getint('PROXY_TIMEOUT', 30)
            self.use_proxy = self.is_proxy_enabled
            self.proxy_retry_times = self.settings.get('PROXY_RETRY_TIMES', 3)
            self.proxy_exclude_spiders = set(self.proxy_spider_exclude)
            self.no_proxy_domains = self.settings.get('NO_PROXY_DOMAINS', [])
        except Exception as e:
            self.logger.error(f"Error loading proxy settings: {e}")
            # 设置默认值
            self.proxy_spider_exclude = []
            self.is_proxy_enabled = True
            self.retry_times = 2
            self.retry_http_codes = [500, 502, 503, 504, 408, 429]
            self.proxy_required = False
            self.proxy_timeout = 30
            self.use_proxy = True
            self.proxy_retry_times = 3
            self.proxy_exclude_spiders = set()
            self.no_proxy_domains = []

    @staticmethod
    def _parse_bool(value: Union[str, bool]) -> bool:
        """安全解析布尔值"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return bool(value)

    def _get_proxy(self, target_url: str = None) -> Optional[str]:
        """获取代理地址，支持智能选择"""
        try:
            # 使用新的代理池管理器
            manager = get_proxy_pool_manager()
            proxy = manager.get_proxy(target_url)

            if proxy:
                # 确保代理格式正确
                if not proxy.startswith(('http://', 'https://')):
                    proxy = f"http://{proxy}"
                return proxy

            return None

        except Exception as e:
            self.logger.error(f"Error getting proxy: {e}")
            return None

    def _should_use_proxy(self, request: Request, spider: Spider) -> bool:
        """判断是否应该使用代理"""
        # 检查请求是否明确指定不使用代理
        if 'no_proxy' in request.meta and request.meta['no_proxy']:
            return False

        # 检查域名是否在不使用代理的列表中
        for domain in self.no_proxy_domains:
            if domain in request.url:
                return False

        # 检查爬虫参数设置
        spider_proxy_setting = getattr(spider, 'proxy', '1')
        if spider_proxy_setting == '0':
            return False

        # 检查爬虫是否在排除列表中
        if spider.name in self.proxy_exclude_spiders:
            return False

        # 检查全局代理设置
        return self.use_proxy

    def process_request(self, request: Request, spider: Spider) -> Optional[Request]:
        """处理请求，设置代理"""
        self.stats['requests_processed'] = self.stats.get('requests_processed', 0) + 1

        # 记录请求开始时间
        request.meta['start_time'] = time.time()

        try:
            # 处理代理设置
            if self._should_use_proxy(request, spider):
                # 检查是否已经尝试过代理但失败
                proxy_failed_count = request.meta.get('proxy_failed_count', 0)
                max_proxy_attempts = 3  # 最大代理尝试次数

                if proxy_failed_count < max_proxy_attempts:
                    # 传递目标URL以支持智能代理选择
                    proxy = self._get_proxy(request.url)
                    if proxy:
                        request.meta['proxy'] = proxy
                        request.meta['proxy_used'] = proxy
                        request.meta['proxy_start_time'] = time.time()  # 记录代理使用开始时间
                        self.stats['proxy_used'] = self.stats.get('proxy_used', 0) + 1
                        self.logger.debug(f"Using proxy {proxy} for {request.url}")
                    else:
                        self.logger.warning(f"Failed to get proxy for {request.url}")
                        if self.proxy_required:
                            raise IgnoreRequest(f"Proxy required but not available for {request.url}")
                else:
                    self.logger.info(f"Skipping proxy for {request.url} after {proxy_failed_count} failed attempts")

            return None

        except IgnoreRequest:
            raise
        except Exception as e:
            self.logger.error(f"Error processing request {request.url}: {e}")
            self.stats['requests_failed'] = self.stats.get('requests_failed', 0) + 1
            return None

    def process_response(self, request: Request, response: Response, spider: Spider) -> Union[Request, Response]:
        """处理响应，报告代理状态"""
        try:
            # 计算响应时间
            start_time = request.meta.get('start_time', 0)
            if start_time > 0:
                response_time = time.time() - start_time
                self.stats['response_times'] = self.stats.get('response_times', [])
                self.stats['response_times'].append(response_time)

                # 报告代理使用情况
                proxy_used = request.meta.get('proxy_used')
                if proxy_used:
                    # 提取代理地址（去掉http://前缀）
                    proxy_addr = proxy_used.replace('http://', '').replace('https://', '')

                    # 计算代理响应时间
                    proxy_start_time = request.meta.get('proxy_start_time', start_time)
                    proxy_response_time = time.time() - proxy_start_time

                    if response.status == 200:
                        report_proxy_success(proxy_addr, proxy_response_time, request.url)
                        self.logger.debug(f"Proxy success: {proxy_addr} for {request.url}")
                    else:
                        report_proxy_failure(proxy_addr, request.url)
                        self.logger.debug(f"Proxy failure: {proxy_addr} for {request.url} (status: {response.status})")

            # 检查响应状态是否需要重试
            if response.status in self.retry_http_codes:
                return self._retry_with_proxy(request, spider, f"status code {response.status}")

            return response

        except Exception as e:
            self.logger.error(f"Error processing response for {request.url}: {e}")
            return response

    def _retry_with_proxy(self, request: Request, spider: Spider, reason: str) -> Union[Request, Response]:
        """使用代理重试请求"""
        retry_count = request.meta.get('retry_times', 0)

        if retry_count >= self.retry_times:
            self.logger.error(f"Max retries exceeded for {request.url}: {reason}")
            raise IgnoreRequest(f"Max retries exceeded: {reason}")

        proxy = self._get_proxy()
        if proxy:
            new_request = request.copy()
            new_request.meta['proxy'] = proxy
            new_request.meta['proxy_used'] = proxy
            new_request.meta['retry_times'] = retry_count + 1
            new_request.meta['start_time'] = time.time()  # 重置开始时间
            new_request.dont_filter = True

            self.stats['requests_retried'] = self.stats.get('requests_retried', 0) + 1
            self.logger.info(f"Retrying {request.url} with proxy {proxy} (attempt {retry_count + 1})")

            return new_request
        else:
            self.logger.error(f"No proxy available for retry: {request.url}")
            if self.proxy_required:
                raise IgnoreRequest(f"No proxy available: {reason}")
            # 不使用代理重试
            new_request = request.copy()
            new_request.meta.pop('proxy', None)
            new_request.meta.pop('proxy_used', None)
            new_request.meta['retry_times'] = retry_count + 1
            new_request.meta['start_time'] = time.time()
            new_request.dont_filter = True
            self.logger.info(f"Retrying {request.url} without proxy due to {reason}")
            return new_request

    def process_exception(self, request: Request, exception: Exception, spider: Spider) -> Optional[Request]:
        """处理请求异常，更换代理重试"""
        try:
            self.logger.warning(f"Request exception for {request.url}: {exception}")
            self.stats['requests_failed'] = self.stats.get('requests_failed', 0) + 1

            # 报告代理失败
            proxy_used = request.meta.get('proxy_used')
            if proxy_used:
                proxy_addr = proxy_used.replace('http://', '').replace('https://', '')
                report_proxy_failure(proxy_addr, request.url)
                self.logger.debug(f"Reported proxy failure: {proxy_addr} for {request.url}")

                # 增加代理失败计数
                proxy_failed_count = request.meta.get('proxy_failed_count', 0) + 1
                request.meta['proxy_failed_count'] = proxy_failed_count

            # 检查是否应该重试
            retry_count = request.meta.get('retry_times', 0)
            if retry_count >= self.retry_times:
                self.logger.error(f"Max retries exceeded for {request.url}")
                return None

            # 智能重试策略：优先用代理重试
            if not proxy_used and self._should_use_proxy(request, spider):
                proxy = self._get_proxy(request.url)  # 传递URL以支持智能选择
                if proxy:
                    new_request = request.copy()
                    new_request.meta['proxy'] = proxy
                    new_request.meta['proxy_used'] = proxy
                    new_request.meta['proxy_start_time'] = time.time()
                    new_request.meta['retry_times'] = retry_count + 1
                    new_request.meta['start_time'] = time.time()
                    new_request.dont_filter = True
                    self.stats['requests_retried'] = self.stats.get('requests_retried', 0) + 1
                    self.logger.info(f"Retrying {request.url} with new proxy due to exception")
                    return new_request

            # 如果已用代理，换新代理继续重试
            if self._should_use_proxy(request, spider):
                proxy = self._get_proxy(request.url)  # 智能选择新代理
                if proxy and proxy != proxy_used:  # 确保不是同一个代理
                    new_request = request.copy()
                    new_request.meta['proxy'] = proxy
                    new_request.meta['proxy_used'] = proxy
                    new_request.meta['proxy_start_time'] = time.time()
                    new_request.meta['retry_times'] = retry_count + 1
                    new_request.meta['start_time'] = time.time()
                    new_request.dont_filter = True
                    self.stats['requests_retried'] = self.stats.get('requests_retried', 0) + 1
                    self.logger.info(f"Retrying {request.url} with new proxy due to exception")
                    return new_request

            # 最后尝试不使用代理
            new_request = request.copy()
            new_request.meta.pop('proxy', None)
            new_request.meta.pop('proxy_used', None)
            new_request.meta['retry_times'] = retry_count + 1
            new_request.meta['start_time'] = time.time()
            new_request.dont_filter = True
            self.logger.info(f"Retrying {request.url} without proxy due to exception")
            return new_request

        except Exception as e:
            self.logger.error(f"Error handling exception for {request.url}: {e}")
            return None