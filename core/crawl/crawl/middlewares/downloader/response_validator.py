# -*- coding: utf-8 -*-

"""
响应验证中间件
检测和处理无效响应，如代理被检测、反爬虫页面等
"""

import re
import json
import logging
from typing import Optional, Union, Dict, Any, List
from scrapy.http import Request, Response
from scrapy.spiders import <PERSON>
from scrapy.exceptions import IgnoreRequest
from ..base import BaseMiddleware

logger = logging.getLogger(__name__)

class ResponseValidatorMiddleware(BaseMiddleware):
    """响应验证中间件"""
    
    def __init__(self, crawler=None):
        super().__init__(crawler)
        self._init_validation_rules()
        
    def _init_validation_rules(self):
        """初始化验证规则"""
        # 无效响应的特征
        self.invalid_response_patterns = [
            # 通用反爬虫页面
            r'<title>Net Video System</title>',
            r'<title>Access Denied</title>',
            r'<title>403 Forbidden</title>',
            r'<title>404 Not Found</title>',
            r'<title>Service Unavailable</title>',
            r'<title>Too Many Requests</title>',
            r'<title>Cloudflare</title>',
            r'<title>Just a moment</title>',
            r'<title>Please wait</title>',
            r'<title>Checking your browser</title>',
            r'<title>Security check</title>',
            r'<title>Verification</title>',
            r'<title>Robot Check</title>',
            r'<title>Anti-Bot</title>',
            
            # 代理检测页面
            r'Proxy detected',
            r'Your IP has been blocked',
            r'Access from your location is not allowed',
            r'VPN detected',
            r'Please disable your proxy',
            
            # 空页面或错误页面
            r'<body>\s*</body>',
            r'<html><head></head><body></body></html>',
            r'Internal Server Error',
            r'Bad Gateway',
            r'Gateway Timeout',
        ]
        
        # 编译正则表达式以提高性能
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.invalid_response_patterns]
        
        # 最小有效响应长度
        self.min_response_length = {
            'json': 20,      # JSON响应至少20字符
            'html': 100,     # HTML响应至少100字符
            'xml': 50,       # XML响应至少50字符
            'default': 10    # 默认最小长度
        }
        
        # 特定网站的验证规则
        self.site_specific_rules = {
            'cls.cn': {
                'expected_content_type': 'application/json',
                'required_fields': ['data', 'data.roll_data'],
                'min_length': 100,
                'invalid_indicators': ['<html>', '<title>Net Video System</title>']
            },
            'xueqiu.com': {
                'expected_content_type': 'application/json',
                'required_fields': ['data'],
                'min_length': 50,
                'invalid_indicators': ['<html>', 'login_required']
            },
            'eastmoney.com': {
                'expected_content_type': 'application/json',
                'required_fields': ['result'],
                'min_length': 100,
                'invalid_indicators': ['<html>', 'error']
            }
        }
    
    def process_response(self, request: Request, response: Response, spider: Spider) -> Union[Request, Response]:
        """处理响应，验证响应有效性"""
        try:
            # 检查响应是否有效
            validation_result = self._validate_response(request, response, spider)
            
            if not validation_result['is_valid']:
                reason = validation_result['reason']
                self.logger.warning(f"Invalid response detected for {request.url}: {reason}")
                
                # 记录无效响应详情
                self._log_invalid_response(request, response, reason)
                
                # 根据配置决定是否重试
                if self._should_retry_invalid_response(request, response, spider):
                    return self._create_retry_request(request, response, spider, reason)
                else:
                    # 不重试，忽略请求
                    raise IgnoreRequest(f"Invalid response: {reason}")
            
            return response
            
        except IgnoreRequest:
            raise
        except Exception as e:
            self.logger.error(f"Error in response validation for {request.url}: {e}")
            return response
    
    def _validate_response(self, request: Request, response: Response, spider: Spider) -> Dict[str, Any]:
        """验证响应有效性"""
        # 基本状态码检查
        if response.status != 200:
            # 特殊处理302重定向
            if response.status == 302:
                location = response.headers.get('Location', b'').decode('utf-8', errors='ignore')
                if 'openapp/open.html' in location:
                    return {
                        'is_valid': False,
                        'reason': f'Redirected to app page: {location}',
                        'needs_session_refresh': True
                    }

            return {
                'is_valid': False,
                'reason': f'Non-200 status code: {response.status}'
            }
        
        # 获取响应内容
        try:
            response_text = response.text
            response_body = response.body
        except Exception as e:
            return {
                'is_valid': False,
                'reason': f'Failed to decode response: {e}'
            }
        
        # 检查响应长度
        content_type = self._get_content_type(response)
        min_length = self.min_response_length.get(content_type, self.min_response_length['default'])
        
        if len(response_body) < min_length:
            return {
                'is_valid': False,
                'reason': f'Response too short: {len(response_body)} bytes (min: {min_length})'
            }
        
        # 检查通用无效响应模式
        for pattern in self.compiled_patterns:
            if pattern.search(response_text):
                return {
                    'is_valid': False,
                    'reason': f'Invalid response pattern detected: {pattern.pattern}'
                }
        
        # 特定网站验证
        domain = self._extract_domain(request.url)
        if domain in self.site_specific_rules:
            site_validation = self._validate_site_specific(response, domain, response_text)
            if not site_validation['is_valid']:
                return site_validation
        
        # JSON响应特殊验证
        if content_type == 'json' or self._looks_like_json(response_text):
            json_validation = self._validate_json_response(response_text, domain)
            if not json_validation['is_valid']:
                return json_validation
        
        return {'is_valid': True, 'reason': 'Valid response'}
    
    def _get_content_type(self, response: Response) -> str:
        """获取响应内容类型"""
        content_type = response.headers.get('Content-Type', b'').decode('utf-8', errors='ignore').lower()
        
        if 'json' in content_type:
            return 'json'
        elif 'html' in content_type:
            return 'html'
        elif 'xml' in content_type:
            return 'xml'
        else:
            return 'default'
    
    def _extract_domain(self, url: str) -> str:
        """提取域名"""
        from urllib.parse import urlparse
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # 移除www前缀
        if domain.startswith('www.'):
            domain = domain[4:]
        
        return domain
    
    def _validate_site_specific(self, response: Response, domain: str, response_text: str) -> Dict[str, Any]:
        """特定网站验证"""
        rules = self.site_specific_rules[domain]
        
        # 检查内容长度
        if len(response.body) < rules.get('min_length', 50):
            return {
                'is_valid': False,
                'reason': f'Response too short for {domain}: {len(response.body)} bytes'
            }
        
        # 检查无效指示器
        for indicator in rules.get('invalid_indicators', []):
            if indicator in response_text:
                return {
                    'is_valid': False,
                    'reason': f'Invalid indicator found for {domain}: {indicator}'
                }
        
        return {'is_valid': True, 'reason': 'Site-specific validation passed'}
    
    def _looks_like_json(self, text: str) -> bool:
        """检查文本是否看起来像JSON"""
        text = text.strip()
        return (text.startswith('{') and text.endswith('}')) or (text.startswith('[') and text.endswith(']'))
    
    def _validate_json_response(self, response_text: str, domain: str = None) -> Dict[str, Any]:
        """验证JSON响应"""
        try:
            json_data = json.loads(response_text)
        except json.JSONDecodeError as e:
            return {
                'is_valid': False,
                'reason': f'Invalid JSON: {e}'
            }
        
        # 检查是否为空对象或数组
        if not json_data:
            return {
                'is_valid': False,
                'reason': 'Empty JSON response'
            }
        
        # 特定网站的JSON字段检查
        if domain and domain in self.site_specific_rules:
            required_fields = self.site_specific_rules[domain].get('required_fields', [])
            for field in required_fields:
                # 支持嵌套字段检查，如 "data.roll_data"
                if '.' in field:
                    field_parts = field.split('.')
                    current_data = json_data
                    field_found = True

                    for part in field_parts:
                        if isinstance(current_data, dict) and part in current_data:
                            current_data = current_data[part]
                        else:
                            field_found = False
                            break

                    if not field_found:
                        return {
                            'is_valid': False,
                            'reason': f'Missing required field for {domain}: {field}'
                        }
                else:
                    # 简单字段检查
                    if field not in json_data:
                        return {
                            'is_valid': False,
                            'reason': f'Missing required field for {domain}: {field}'
                        }
        
        # 检查常见的错误响应
        if isinstance(json_data, dict):
            # 检查错误字段 (注意：error=0通常表示成功)
            if 'error' in json_data and json_data['error'] != 0:
                return {
                    'is_valid': False,
                    'reason': f'Error in JSON response: {json_data["error"]}'
                }

            # 检查其他错误字段
            error_fields = ['err', 'error_msg', 'error_message']
            for field in error_fields:
                if field in json_data and json_data[field]:
                    return {
                        'is_valid': False,
                        'reason': f'Error in JSON response: {json_data[field]}'
                    }

            # 检查message字段（只有在没有data字段时才认为是错误）
            if 'message' in json_data and json_data['message'] and 'data' not in json_data:
                return {
                    'is_valid': False,
                    'reason': f'Error in JSON response: {json_data["message"]}'
                }
            
            # 检查状态字段
            if 'status' in json_data and json_data['status'] != 'success' and json_data['status'] != 200:
                return {
                    'is_valid': False,
                    'reason': f'Error status in JSON: {json_data["status"]}'
                }
        
        return {'is_valid': True, 'reason': 'Valid JSON response'}
    
    def _should_retry_invalid_response(self, request: Request, response: Response, spider: Spider) -> bool:
        """判断是否应该重试无效响应"""
        # 检查重试次数
        retry_times = request.meta.get('retry_times', 0)
        max_retry_times = self.settings.getint('RETRY_TIMES', 2)

        if retry_times >= max_retry_times:
            return False

        # 检查是否需要会话刷新
        if request.meta.get('needs_session_refresh'):
            return True

        # 检查是否使用了代理
        proxy_used = request.meta.get('proxy_used')
        if proxy_used:
            # 如果使用了代理且响应无效，可能是代理问题，值得重试
            return True

        # 检查响应状态码
        if response.status in [302, 403, 429, 503]:
            # 这些状态码可能是临时的，值得重试
            return True

        return False
    
    def _create_retry_request(self, request: Request, response: Response, spider: Spider, reason: str) -> Request:
        """创建重试请求"""
        retry_times = request.meta.get('retry_times', 0) + 1

        new_request = request.copy()
        new_request.meta['retry_times'] = retry_times
        new_request.meta['retry_reason'] = reason
        new_request.dont_filter = True

        # 如果需要会话刷新，尝试刷新会话
        if request.meta.get('needs_session_refresh'):
            self._handle_session_refresh(new_request, spider)

        # 如果之前使用了代理，标记代理失败
        proxy_used = request.meta.get('proxy_used')
        if proxy_used:
            new_request.meta['proxy_failed'] = True
            # 清除代理，让代理中间件重新分配
            if 'proxy' in new_request.meta:
                del new_request.meta['proxy']

        self.logger.info(f"Retrying {request.url} due to invalid response (attempt {retry_times}): {reason}")

        return new_request

    def _handle_session_refresh(self, request: Request, spider: Spider):
        """处理会话刷新"""
        try:
            # 检查是否是财联社爬虫
            if hasattr(spider, 'name') and spider.name == 'cls':
                from crawl.tools.cls_session_manager import get_cls_session_manager

                session_manager = get_cls_session_manager()
                success = session_manager.refresh_session()

                if success:
                    # 更新请求的cookies和headers
                    new_cookies = session_manager.get_valid_cookies()
                    new_headers = session_manager.get_api_headers()

                    # 更新请求
                    if new_cookies:
                        request.cookies = new_cookies

                    if new_headers:
                        request.headers.update(new_headers)

                    self.logger.info("Session refreshed successfully for retry")
                else:
                    self.logger.warning("Failed to refresh session for retry")

        except Exception as e:
            self.logger.error(f"Error handling session refresh: {e}")
    
    def _log_invalid_response(self, request: Request, response: Response, reason: str):
        """记录无效响应的详细信息"""
        self.logger.warning(f"Invalid response details for {request.url}:")
        self.logger.warning(f"  Reason: {reason}")
        self.logger.warning(f"  Status: {response.status}")
        self.logger.warning(f"  Headers: {dict(response.headers)}")
        self.logger.warning(f"  Body length: {len(response.body)}")

        # 记录响应内容的前500字符用于调试
        try:
            preview = response.text[:500]
            self.logger.warning(f"  Body preview: {preview}")

            # 如果是JSON，尝试解析并显示结构
            if response.text.strip().startswith('{'):
                try:
                    import json
                    json_data = json.loads(response.text)
                    keys = list(json_data.keys()) if isinstance(json_data, dict) else "Not a dict"
                    self.logger.warning(f"  JSON keys: {keys}")

                    if isinstance(json_data, dict) and 'data' in json_data:
                        data_keys = list(json_data['data'].keys()) if isinstance(json_data['data'], dict) else "Not a dict"
                        self.logger.warning(f"  Data keys: {data_keys}")

                except json.JSONDecodeError:
                    self.logger.warning(f"  JSON parsing failed")

        except Exception as e:
            self.logger.warning(f"  Body preview error: {e}")

        # 更新统计
        self.stats['invalid_responses'] = self.stats.get('invalid_responses', 0) + 1
