# -*- coding: utf-8 -*-

"""
东方财富反爬机制处理中间件
专门处理eastmoney.com的IP封禁和连接限制问题
"""

import time
import random
import logging
from typing import Optional, Union, Dict, Set
from urllib.parse import urlparse

from scrapy.http import Request, Response
from scrapy.spiders import <PERSON>
from scrapy.exceptions import IgnoreRequest
from scrapy.downloadermiddlewares.retry import RetryMiddleware as BaseRetryMiddleware

from ..base import BaseMiddleware

logger = logging.getLogger(__name__)

class EastmoneyAntiBanMiddleware(BaseMiddleware):
    """东方财富反封禁中间件"""
    
    def __init__(self, crawler=None):
        super().__init__(crawler)
        
        # 反爬配置
        self.ban_detection_keywords = [
            'Connection was closed cleanly',
            'Connection lost',
            'Connection timeout',
            'Too Many Requests',
            'Access Denied',
            'Forbidden',
            'Service Unavailable'
        ]
        
        # IP封禁检测
        self.ip_ban_indicators = {
            'connection_failures': 0,
            'last_failure_time': 0,
            'consecutive_failures': 0,
            'banned_ips': set(),
            'cooling_down': False,
            'cooldown_start': 0
        }
        
        # 请求频率控制
        self.request_intervals = {
            'min_delay': 2.0,      # 最小延迟2秒
            'max_delay': 8.0,      # 最大延迟8秒
            'ban_delay': 30.0,     # 检测到封禁时的延迟
            'cooldown_delay': 300.0 # 冷却期5分钟
        }
        
        # 主机轮换
        self.eastmoney_hosts = ['59', '60', '61', '62', '63']
        self.host_status = {host: {'failures': 0, 'last_used': 0, 'banned': False} 
                           for host in self.eastmoney_hosts}
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'failed_requests': 0,
            'banned_detections': 0,
            'host_switches': 0,
            'cooldowns_triggered': 0
        }
        
        logger.info("EastmoneyAntiBanMiddleware initialized")
    
    def process_request(self, request: Request, spider: Spider) -> Optional[Request]:
        """处理请求前的反爬检查"""
        if not self._is_eastmoney_request(request):
            return None
        
        self.stats['total_requests'] += 1
        
        # 检查是否在冷却期
        if self._is_in_cooldown():
            logger.warning(f"In cooldown period, delaying request for {request.url}")
            time.sleep(self.request_intervals['cooldown_delay'])
            self.ip_ban_indicators['cooling_down'] = False
        
        # 智能延迟
        delay = self._calculate_smart_delay(request)
        if delay > 0:
            logger.debug(f"Anti-ban delay: {delay:.2f}s for {request.url}")
            time.sleep(delay)
        
        # 优化请求头
        self._optimize_request_headers(request)
        
        # 主机轮换
        request = self._rotate_host_if_needed(request)
        
        return None
    
    def process_response(self, request: Request, response: Response, spider: Spider) -> Union[Request, Response]:
        """处理响应，检测反爬机制"""
        if not self._is_eastmoney_request(request):
            return response
        
        # 检测封禁信号
        if self._detect_ban_signals(request, response):
            return self._handle_ban_detection(request, spider)
        
        # 检测空响应或异常响应
        if self._is_suspicious_response(response):
            return self._handle_suspicious_response(request, response, spider)
        
        # 成功响应，重置失败计数
        self._reset_failure_counters(request)
        
        return response
    
    def process_exception(self, request: Request, exception: Exception, spider: Spider) -> Optional[Request]:
        """处理异常，特别是连接异常"""
        if not self._is_eastmoney_request(request):
            return None
        
        self.stats['failed_requests'] += 1
        exception_str = str(exception)
        
        # 检测是否是反爬相关的异常
        if any(keyword in exception_str for keyword in self.ban_detection_keywords):
            logger.warning(f"Detected anti-crawling exception for {request.url}: {exception_str}")
            return self._handle_ban_detection(request, spider)
        
        # 其他异常的处理
        return self._handle_general_exception(request, exception, spider)
    
    def _is_eastmoney_request(self, request: Request) -> bool:
        """判断是否是东方财富的请求"""
        return 'eastmoney.com' in request.url or 'push2.eastmoney.com' in request.url
    
    def _is_in_cooldown(self) -> bool:
        """检查是否在冷却期"""
        if not self.ip_ban_indicators['cooling_down']:
            return False
        
        cooldown_duration = time.time() - self.ip_ban_indicators['cooldown_start']
        return cooldown_duration < self.request_intervals['cooldown_delay']
    
    def _calculate_smart_delay(self, request: Request) -> float:
        """计算智能延迟"""
        base_delay = self.request_intervals['min_delay']
        
        # 根据失败次数增加延迟
        failure_multiplier = min(self.ip_ban_indicators['consecutive_failures'] * 0.5, 3.0)
        
        # 根据最近失败时间增加延迟
        time_since_failure = time.time() - self.ip_ban_indicators['last_failure_time']
        if time_since_failure < 60:  # 1分钟内有失败
            recency_multiplier = 2.0
        else:
            recency_multiplier = 1.0
        
        # 计算最终延迟
        delay = base_delay * failure_multiplier * recency_multiplier
        
        # 添加随机性
        delay += random.uniform(0, 1.0)
        
        return min(delay, self.request_intervals['max_delay'])
    
    def _optimize_request_headers(self, request: Request):
        """优化请求头以避免检测"""
        # 确保有合适的Referer
        if 'Referer' not in request.headers:
            request.headers['Referer'] = 'http://quote.eastmoney.com/'
        
        # 添加更真实的请求头
        request.headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
    
    def _rotate_host_if_needed(self, request: Request) -> Request:
        """根据需要轮换主机"""
        if 'push2.eastmoney.com' not in request.url:
            return request
        
        current_host = self._extract_host_from_url(request.url)
        if current_host and self.host_status[current_host]['banned']:
            # 当前主机被封，切换到可用主机
            new_host = self._get_best_available_host()
            if new_host and new_host != current_host:
                new_url = request.url.replace(f'{current_host}.push2.eastmoney.com', 
                                            f'{new_host}.push2.eastmoney.com')
                request = request.replace(url=new_url)
                self.stats['host_switches'] += 1
                logger.info(f"Switched host from {current_host} to {new_host}")
        
        return request
    
    def _extract_host_from_url(self, url: str) -> Optional[str]:
        """从URL中提取主机编号"""
        try:
            parsed = urlparse(url)
            hostname = parsed.hostname
            if hostname and '.push2.eastmoney.com' in hostname:
                return hostname.split('.')[0]
        except:
            pass
        return None
    
    def _get_best_available_host(self) -> Optional[str]:
        """获取最佳可用主机"""
        available_hosts = [host for host, status in self.host_status.items() 
                          if not status['banned']]
        
        if not available_hosts:
            # 所有主机都被封，重置状态
            logger.warning("All hosts appear to be banned, resetting host status")
            for host in self.host_status:
                self.host_status[host]['banned'] = False
                self.host_status[host]['failures'] = 0
            available_hosts = list(self.host_status.keys())
        
        # 选择失败次数最少且最久未使用的主机
        best_host = min(available_hosts, 
                       key=lambda h: (self.host_status[h]['failures'], 
                                    self.host_status[h]['last_used']))
        
        self.host_status[best_host]['last_used'] = time.time()
        return best_host
    
    def _detect_ban_signals(self, request: Request, response: Response) -> bool:
        """检测封禁信号"""
        # HTTP状态码检测
        if response.status in [403, 429, 503, 504]:
            return True
        
        # 响应内容检测
        if response.body:
            body_text = response.text.lower()
            ban_keywords = ['access denied', 'forbidden', 'too many requests', 
                           'service unavailable', 'blocked', 'banned']
            if any(keyword in body_text for keyword in ban_keywords):
                return True
        
        # 响应大小异常检测
        if len(response.body) < 100:  # 异常小的响应
            return True
        
        return False
    
    def _is_suspicious_response(self, response: Response) -> bool:
        """检测可疑响应"""
        # 空响应
        if not response.body:
            return True

        # JSON响应检测
        content_type = response.headers.get('Content-Type', b'').decode('utf-8', errors='ignore')
        if 'application/json' in content_type:
            try:
                import json
                data = json.loads(response.text)
                # 检查是否是错误响应
                if isinstance(data, dict):
                    if data.get('error') or data.get('code') != 0:
                        return True
                    # 检查是否缺少预期数据
                    if 'data' not in data or not data['data']:
                        return True
            except:
                return True

        return False
    
    def _handle_ban_detection(self, request: Request, spider: Spider) -> Request:
        """处理检测到的封禁"""
        self.stats['banned_detections'] += 1
        self.ip_ban_indicators['consecutive_failures'] += 1
        self.ip_ban_indicators['last_failure_time'] = time.time()
        
        # 标记当前主机为被封
        current_host = self._extract_host_from_url(request.url)
        if current_host:
            self.host_status[current_host]['banned'] = True
            self.host_status[current_host]['failures'] += 1
        
        # 触发冷却期
        if self.ip_ban_indicators['consecutive_failures'] >= 3:
            self._trigger_cooldown()
        
        # 创建重试请求
        return self._create_retry_request(request, "Ban detected")
    
    def _handle_suspicious_response(self, request: Request, response: Response, spider: Spider) -> Request:
        """处理可疑响应"""
        logger.warning(f"Suspicious response from {request.url}: status={response.status}, size={len(response.body)}")
        
        self.ip_ban_indicators['consecutive_failures'] += 1
        
        # 如果连续可疑响应过多，触发主机切换
        if self.ip_ban_indicators['consecutive_failures'] >= 2:
            current_host = self._extract_host_from_url(request.url)
            if current_host:
                self.host_status[current_host]['failures'] += 1
        
        return self._create_retry_request(request, "Suspicious response")
    
    def _handle_general_exception(self, request: Request, exception: Exception, spider: Spider) -> Optional[Request]:
        """处理一般异常"""
        retry_times = request.meta.get('retry_times', 0)
        max_retry_times = self.settings.get('RETRY_TIMES', 2)
        
        if retry_times < max_retry_times:
            return self._create_retry_request(request, f"Exception: {exception}")
        
        return None
    
    def _trigger_cooldown(self):
        """触发冷却期"""
        self.ip_ban_indicators['cooling_down'] = True
        self.ip_ban_indicators['cooldown_start'] = time.time()
        self.stats['cooldowns_triggered'] += 1
        
        logger.warning(f"Triggered cooldown period for {self.request_intervals['cooldown_delay']} seconds")
    
    def _reset_failure_counters(self, request: Request):
        """重置失败计数器"""
        self.ip_ban_indicators['consecutive_failures'] = max(0, 
            self.ip_ban_indicators['consecutive_failures'] - 1)
        
        # 重置主机状态
        current_host = self._extract_host_from_url(request.url)
        if current_host and self.host_status[current_host]['failures'] > 0:
            self.host_status[current_host]['failures'] -= 1
            if self.host_status[current_host]['failures'] == 0:
                self.host_status[current_host]['banned'] = False
    
    def _create_retry_request(self, request: Request, reason: str) -> Request:
        """创建重试请求"""
        retry_times = request.meta.get('retry_times', 0) + 1
        max_retry_times = self.settings.get('RETRY_TIMES', 2)
        
        if retry_times > max_retry_times:
            logger.error(f"Max retries exceeded for {request.url}: {reason}")
            raise IgnoreRequest(f"Max retries exceeded: {reason}")
        
        # 创建新请求
        new_request = request.copy()
        new_request.meta['retry_times'] = retry_times
        new_request.meta['retry_reason'] = reason
        new_request.dont_filter = True
        
        # 强制切换主机
        new_request = self._rotate_host_if_needed(new_request)
        
        # 增加延迟
        delay = self.request_intervals['ban_delay'] + random.uniform(0, 10)
        new_request.meta['download_delay'] = delay
        
        logger.info(f"Retrying {request.url} (attempt {retry_times}) with {delay:.1f}s delay: {reason}")
        
        return new_request
    
    def spider_closed(self, spider):
        """爬虫关闭时输出统计信息"""
        if spider.name in ['eastmoney', 'eastmoney2', 'eastmoney3']:
            logger.info("EastmoneyAntiBanMiddleware Statistics:")
            logger.info(f"  Total requests: {self.stats['total_requests']}")
            logger.info(f"  Failed requests: {self.stats['failed_requests']}")
            logger.info(f"  Ban detections: {self.stats['banned_detections']}")
            logger.info(f"  Host switches: {self.stats['host_switches']}")
            logger.info(f"  Cooldowns triggered: {self.stats['cooldowns_triggered']}")
            
            # 主机状态
            logger.info("Host status:")
            for host, status in self.host_status.items():
                logger.info(f"  Host {host}: failures={status['failures']}, banned={status['banned']}")
