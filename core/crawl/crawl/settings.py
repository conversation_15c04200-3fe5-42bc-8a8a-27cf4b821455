# -*- coding: utf-8 -*-

"""
Scrapy settings for crawl project

优化后的全局配置，包含：
- 性能优化设置
- 网络连接优化
- 错误处理改进
- 中间件配置优化
- 日志和监控配置

更多配置信息请参考：
https://doc.scrapy.org/en/latest/topics/settings.html
"""

import os
import scrapy

# =============================================================================
# 基础配置
# =============================================================================

BOT_NAME = 'crawl'


# 使用asyncio reactor以支持异步操作
try:
    from twisted.internet import asyncioreactor
    asyncioreactor.install()
except ImportError:
    pass




SPIDER_MODULES = ['crawl.spiders']
NEWSPIDER_MODULE = 'crawl.spiders'

# =============================================================================
# 网络和性能优化
# =============================================================================

# 下载超时设置 - 根据测试结果适当增加
DOWNLOAD_TIMEOUT = 45  # 从30秒增加到45秒，减少超时失败

# 并发请求数 - 适度降低以提高稳定性
CONCURRENT_REQUESTS = 16  # 从32降低到16，减少服务器压力
CONCURRENT_REQUESTS_PER_DOMAIN = 8  # 每个域名最大并发数
CONCURRENT_REQUESTS_PER_IP = 4      # 每个IP最大并发数

# DNS查询线程池大小
REACTOR_THREADPOOL_MAXSIZE = 20

# 请求延迟设置
DOWNLOAD_DELAY = 1.5  # 从2秒降低到1.5秒，平衡速度和稳定性
RANDOMIZE_DOWNLOAD_DELAY = 0.5  # 随机延迟范围 (0.5 * to 1.5 * DOWNLOAD_DELAY)

# 东方财富专用配置
EASTMONEY_ANTI_BAN_ENABLED = True
EASTMONEY_MIN_DELAY = 2.0           # 东方财富最小延迟
EASTMONEY_MAX_DELAY = 8.0           # 东方财富最大延迟
EASTMONEY_BAN_DELAY = 30.0          # 检测到封禁时的延迟
EASTMONEY_COOLDOWN_DELAY = 300.0    # 冷却期延迟（5分钟）
EASTMONEY_MAX_REQUESTS_PER_MINUTE = 20  # 每分钟最大请求数

# =============================================================================
# 重试和错误处理优化
# =============================================================================

# 重试配置 - 优化重试策略
RETRY_ENABLED = True
RETRY_TIMES = 5  # 从30降低到5，避免过度重试
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]

# 重定向配置
REDIRECT_ENABLED = True
REDIRECT_MAX_TIMES = 5

# HTTP错误处理
HTTPERROR_ALLOWED_CODES = [307, 302, 301]

# =============================================================================
# User-Agent和请求头优化
# =============================================================================

# User-Agent配置 - 使用列表形式以兼容RandomUserAgentMiddleware
USER_AGENT = [
   "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.122 Safari/537.36"
]

# 注释掉默认请求头，让爬虫使用原有的请求头配置
# DEFAULT_REQUEST_HEADERS = {
#     'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
#     'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
#     'Accept-Encoding': 'gzip, deflate, br',
#     'Cache-Control': 'no-cache',
#     'Pragma': 'no-cache',
# }

# Robots.txt遵守设置
ROBOTSTXT_OBEY = False

# AJAX爬取支持 (已禁用废弃的AjaxCrawlMiddleware)
AJAXCRAWL_ENABLED = False

# =============================================================================
# Cookie和会话管理
# =============================================================================

COOKIES_ENABLED = True
COOKIES_DEBUG = False  # 生产环境关闭调试

# =============================================================================
# 爬虫关闭条件优化
# =============================================================================

CLOSESPIDER_TIMEOUT = 60 * 30  # 30分钟超时，从2小时降低
CLOSESPIDER_ERRORCOUNT = 50    # 增加错误容忍度，从10增加到50
# CLOSESPIDER_ITEMCOUNT = 1000  # 可选：限制抓取项目数
# CLOSESPIDER_PAGECOUNT = 100   # 可选：限制抓取页面数

# =============================================================================
# 日志配置
# =============================================================================

LOG_LEVEL = 'DEBUG'
LOG_ENABLED = True
LOG_ENCODING = 'utf-8'
LOG_DATEFORMAT = '%Y-%m-%d %H:%M:%S'
LOG_FORMAT = '%(levelname)s: %(message)s'

# 日志文件配置（可选）
# LOG_FILE = 'scrapy.log'
# LOG_STDOUT = False

# =============================================================================
# Telnet控制台
# =============================================================================

TELNETCONSOLE_ENABLED = True
TELNETCONSOLE_PORT = [6023, 6073]

# =============================================================================
# 中间件配置优化
# =============================================================================

# Spider中间件配置
# 数字越小，中间件执行优先级越高
SPIDER_MIDDLEWARES = {
    # Scrapy内置中间件
    'scrapy.spidermiddlewares.httperror.HttpErrorMiddleware': 50,
    'scrapy.downloadermiddlewares.offsite.OffsiteMiddleware': 500,
    'scrapy.spidermiddlewares.referer.RefererMiddleware': 700,
    'scrapy.spidermiddlewares.urllength.UrlLengthMiddleware': 800,
    'scrapy.spidermiddlewares.depth.DepthMiddleware': 900,
    
    # 自定义爬虫中间件
    'crawl.middlewares.spider.crawl.CrawlSpiderMiddleware': 600,
}

# 下载器中间件配置
# 数字越小，中间件执行优先级越高
DOWNLOADER_MIDDLEWARES = {
    # 基础中间件
    'scrapy.downloadermiddlewares.robotstxt.RobotsTxtMiddleware': 100,
    'scrapy.downloadermiddlewares.httpauth.HttpAuthMiddleware': 300,
    'scrapy.downloadermiddlewares.downloadtimeout.DownloadTimeoutMiddleware': 350,
    'scrapy.downloadermiddlewares.defaultheaders.DefaultHeadersMiddleware': 400,
    'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': 500,

    # 自定义中间件
    'crawl.middlewares.downloader.user_agent.UserAgentMiddleware': 550,  # 自定义UserAgent
    'crawl.middlewares.downloader.playwright.PlaywrightMiddleware': 560,  # Playwright渲染

    # 重定向中间件
    'scrapy.downloadermiddlewares.redirect.MetaRefreshMiddleware': 580,
    'scrapy.downloadermiddlewares.redirect.RedirectMiddleware': 590,

    # 重试和代理中间件
    'crawl.middlewares.downloader.retry.RetryMiddleware': 600,  # 自定义重试逻辑
    'crawl.middlewares.downloader.proxy.ProxyMiddleware': 650,  # 代理池

    # 东方财富反爬中间件
    'crawl.middlewares.downloader.eastmoney_anti_ban.EastmoneyAntiBanMiddleware': 670,  # 东方财富反爬

    # 响应验证中间件
    'crawl.middlewares.downloader.response_validator.ResponseValidatorMiddleware': 680,  # 响应验证

    # Cookie中间件
    'scrapy.downloadermiddlewares.cookies.CookiesMiddleware': 700,

    # 压缩和统计中间件
    'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware': 810,
    'scrapy.downloadermiddlewares.stats.DownloaderStats': 850,
}

# 下载处理器配置
# 只对需要浏览器渲染的请求使用Playwright，其他使用默认处理器
DOWNLOAD_HANDLERS = {
    # 默认使用Scrapy内置处理器，避免不必要的Playwright初始化
    # "http": "scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler",
    # "https": "scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler",
}

# ========== Playwright 配置 ==========
# 只在需要时使用 Playwright，避免重复的 "Starting download handler" 日志

# Playwright浏览器类型，可选'chromium', 'firefox', 'webkit'
PLAYWRIGHT_BROWSER_TYPE = 'chromium'
PLAYWRIGHT_LAUNCH_OPTIONS = {
    'headless': True,
    'timeout': 60 * 1000,  # 60秒
}

# Playwright默认超时时间
PLAYWRIGHT_DEFAULT_NAVIGATION_TIMEOUT = 60 * 1000

# 需要使用 Playwright 的爬虫列表（按需启用）
PLAYWRIGHT_ENABLED_SPIDERS = [
    # 'xueqiu-detail',  # 雪球详情页面需要JS渲染
    # 'weibo-detail',   # 微博详情页面需要JS渲染
    # 添加其他需要浏览器渲染的爬虫
]

# 需要使用 Playwright 的域名列表
PLAYWRIGHT_ENABLED_DOMAINS = [
    # 'xueqiu.com',     # 雪球网站
    # 'weibo.com',      # 微博网站
    # 添加其他需要浏览器渲染的域名
]

# =============================================================================
# 扩展配置
# =============================================================================

EXTENSIONS = {
    'scrapy.extensions.corestats.CoreStats': 0,
    'scrapy.extensions.telnet.TelnetConsole': 500,
    'scrapy.extensions.memusage.MemoryUsage': 500,
    'scrapy.extensions.logstats.LogStats': 500,
    'scrapy.extensions.closespider.CloseSpider': 500,
    'scrapy.extensions.throttle.AutoThrottle': 500,
    'spidermon.contrib.scrapy.extensions.Spidermon': 500,
}

# =============================================================================
# SpiderMon监控配置
# =============================================================================

SPIDERMON_ENABLED = True
SPIDERMON_SPIDER_CLOSE_MONITORS = (
    'crawl.monitors.SpiderCloseMonitorSuite',
)

# =============================================================================
# Pipeline配置
# =============================================================================

ITEM_PIPELINES = {
    'crawl.pipelines.CrawlPipeline': 300,
}

# =============================================================================
# AutoThrottle自动限速配置
# =============================================================================

AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 1        # 初始下载延迟
AUTOTHROTTLE_MAX_DELAY = 10         # 最大下载延迟
AUTOTHROTTLE_TARGET_CONCURRENCY = 2.0  # 目标并发数
AUTOTHROTTLE_DEBUG = False          # 生产环境关闭调试

# =============================================================================
# HTTP缓存配置
# =============================================================================

HTTPCACHE_ENABLED = False  # 默认关闭，可根据需要开启
HTTPCACHE_EXPIRATION_SECS = 3600  # 缓存1小时
HTTPCACHE_DIR = 'httpcache'
HTTPCACHE_IGNORE_HTTP_CODES = [500, 502, 503, 504, 408, 429]
HTTPCACHE_STORAGE = 'scrapy.extensions.httpcache.FilesystemCacheStorage'

# 去重过滤器配置 - 将在Redis分布式配置中覆盖
DUPEFILTER_CLASS = 'scrapy.dupefilters.RFPDupeFilter'


# =============================================================================
# 通知配置（可选）
# =============================================================================

# Telegram通知配置（如需要可取消注释）
# SPIDERMON_TELEGRAM_SENDER_TOKEN = '**********************************************'
# SPIDERMON_TELEGRAM_RECIPIENTS = ['chat_id', 'group_id', '@channelname']


# =============================================================================
# 代理配置
# =============================================================================

# 代理服务器地址 (使用环境变量配置)
PROXY_HOST = os.getenv('BB_PROXY_HOST', 'http://***********:5010')
# PROXY_HOST2 已移除 - 现在使用单一高质量代理源

# 代理启用控制
BB_SCRAPY_PROXY = True

# 代理配置优化
PROXY_REQUIRED = False  # 是否强制要求代理
PROXY_TIMEOUT = 30      # 代理超时时间
PROXY_MAX_RETRIES = 3   # 代理最大重试次数
PROXY_HEALTH_CHECK_INTERVAL = 300  # 代理健康检查间隔（秒）
PROXY_MIN_SUCCESS_RATE = 0.5  # 代理最低成功率
PROXY_MAX_FAIL_COUNT = 3      # 代理最大失败次数

# 代理验证配置
PROXY_VALIDATION_ENABLED = False  # 是否启用代理验证（简化模式：默认关闭以提高速度）
PROXY_VALIDATION_TIMEOUT = 5  # 代理验证超时时间（秒）
PROXY_FETCH_TIMEOUT = 5       # 代理获取超时时间（秒）
PROXY_MAX_ATTEMPTS = 3        # 单次请求最大代理尝试次数

# 代理负载均衡配置
PROXY_LOAD_BALANCING = True   # 启用代理负载均衡
PROXY_ROTATION_STRATEGY = 'score'  # 代理轮换策略：score/random/round_robin

# 不使用代理的爬虫列表
PROXY_SPIDER_EXCLUDE = [
    'eastmoney', 'eastmoney2',
    'weibo', 'stock-detail', 'all-stock',
    'shibor'  # 添加测试中成功的爬虫
]

# 代理性能监控
PROXY_PERFORMANCE_MONITORING = True  # 启用代理性能监控
PROXY_STATS_ENABLED = True           # 启用代理统计
PROXY_SLOW_REQUEST_THRESHOLD = 10.0  # 慢请求阈值（秒）

# 多维度代理质量管理 (新增)
PROXY_QUALITY_API = 'http://***********:5010'  # 质量管理API地址
PROXY_SMART_SELECTION = True                    # 启用智能代理选择
PROXY_QUALITY_FEEDBACK = True                   # 启用质量反馈
PROXY_DOMAIN_SPECIFIC = True                    # 启用域名特定代理选择

# =============================================================================
# 数据库配置
# =============================================================================

# MySQL数据库配置 - 使用环境变量
MYSQL_HOST = os.getenv('BB_MYSQL_HOST', '')
MYSQL_DBNAME = os.getenv('BB_MYSQL_DBNAME', '')
MYSQL_USER = os.getenv('BB_MYSQL_USER', '')
MYSQL_PASSWD = os.getenv('BB_MYSQL_PASSWD', '')
MYSQL_PORT = int(os.getenv('BB_MYSQL_PORT', '3306'))

# 确保必要的数据库环境变量已设置
assert MYSQL_HOST, "BB_MYSQL_HOST environment variable is not set"
assert MYSQL_DBNAME, "BB_MYSQL_DBNAME environment variable is not set"
assert MYSQL_USER, "BB_MYSQL_USER environment variable is not set"
assert MYSQL_PASSWD, "BB_MYSQL_PASSWD environment variable is not set"

# =============================================================================
# 性能监控和统计
# =============================================================================

# 内存使用监控
MEMUSAGE_ENABLED = True
MEMUSAGE_LIMIT_MB = 2048  # 2GB内存限制
MEMUSAGE_WARNING_MB = 1536  # 1.5GB警告阈值

# 统计收集
STATS_CLASS = 'scrapy.statscollectors.MemoryStatsCollector'

# =============================================================================
# 安全和稳定性设置
# =============================================================================

# DNS超时设置
DNSCACHE_ENABLED = True
DNSCACHE_SIZE = 10000
DNS_TIMEOUT = 60

# 请求队列大小限制
SCHEDULER_MEMORY_QUEUE = 'scrapy.squeues.LifoMemoryQueue'

# URL长度限制
URLLENGTH_LIMIT = 2083

# 深度限制
DEPTH_LIMIT = 10
DEPTH_STATS_VERBOSE = True

# =============================================================================
# Redis分布式配置 (scrapy-redis)
# =============================================================================

# 启用Redis调度器
SCHEDULER = "scrapy_redis.scheduler.Scheduler"

# 启用Redis去重过滤器
DUPEFILTER_CLASS = "scrapy_redis.dupefilter.RFPDupeFilter"

# 不清理Redis队列，允许暂停/恢复爬虫
SCHEDULER_PERSIST = True

# Redis连接配置
REDIS_HOST = os.getenv('BB_REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('BB_REDIS_PORT', '6379'))
REDIS_DB = int(os.getenv('BB_REDIS_CRAWL_DB', '8'))
REDIS_PASSWORD = os.getenv('BB_REDIS_PASSWORD', None)

# Redis连接参数
REDIS_PARAMS = {
    'host': REDIS_HOST,
    'port': REDIS_PORT,
    'db': REDIS_DB,
}

# 如果有密码，添加到连接参数中
if REDIS_PASSWORD:
    REDIS_PARAMS['password'] = REDIS_PASSWORD

# Redis连接池配置
REDIS_START_URLS_AS_SET = True  # 使用set存储起始URL，避免重复
REDIS_START_URLS_KEY = '%(name)s:start_urls'  # 起始URL的Redis key格式

# =============================================================================
# 开发和调试设置
# =============================================================================

# 开发模式设置（生产环境应设为False）
DEVELOPMENT_MODE = os.getenv('BB_SCRAPY_DEVELOPMENT', 'False').lower() == 'true'

if DEVELOPMENT_MODE:
    # 开发模式下的特殊设置
    LOG_LEVEL = 'DEBUG'
    COOKIES_DEBUG = True
    AUTOTHROTTLE_DEBUG = True
    HTTPCACHE_ENABLED = True

